openapi: 3.0.0
servers:
  - url: https://api.monoova.com
    description: Production URL#
  - url: https://sand-api.monoova.com
    description: Sandbox URL
info:
  description: |
    # Introduction

    The Monoova PayTo API is designed to efficiently handle real time financial transactions, providing a reliable platform for creating payment agreements and initiating payments. It offers a streamlined transaction process, ensuring speed and security for businesses and users with benefits above and beyond a direct debit alternative.

    Monoova's PayTo API supports synchronous and asynchronous operations, allowing flexible integration based on varying business needs. The asynchronous API option is particularly beneficial for handling batch operations requiring extended processing time, enhancing overall system performance and reliability. Additionally, Monoova provides webhook subscriptions, enabling users to receive timely updates and notifications about transaction events, which helps maintain transparency and control over financial activities.

    ## Process

    PayTo allows you to debit an enabled domestic bank account in real time. 

    * First, you create a **payment agreement** between a payer and a payee. The terms of the agreement determine the amount and schedule of allowed debits. When you create a Payment Agreement, the account is validated in real time, so you can ensure the account details are correct.

    * Next, the payer authorises the agreement through their banking app.

    * Finally, real time **payment initiations** can be triggered. If successful, funds will be credited to the payee account in real time and made available for disbursement. Because recipients can access funds faster than traditional direct debit systems, this is particularly beneficial for businesses or individuals relying on prompt cash flow payments.

    * If you would like to modify the terms of the agreement, you can use the amendment API's.

    # Getting Started

    1. Access the Domestic Payments Portal:

        * Sandbox Environment:
            - Register: If you are new, [register](https://sandbox.monoova.com) to gain Sandbox access.
            - Login: If you already have Sandbox credentials, [log in](https://sandbox.monoova.com) to continue your work.

        * Production Environment:
            - Login: Login to the [Production portal](https://payments.monoova.com) using the credentials you obtained during onboarding.

    2. Activate Your API Key

        * Generate API Key:
          - If you do not know your API Key or need to reset it, navigate to Manage > Accounts to generate or regenerate your API Key after logging into the portal.

        * Activate API Gateway Access:
          - Go to Manage > API Gateway and enter your API Key to activate API Gateway access for your mAccount.

    3. Configure API Access

        Please [contact our integration team](https://www.monoova.com/contact) to configure your account settings. This step ensures that your account is allowed to access the service.

    4. Explore API Endpoints

        Use the API documentation to plan your integration. It provides details and examples of usage.

    5. Onboarding and Support

        Our integration team will guide you from the initial setup to the final running implementation, providing support throughout the process.

    # Sandbox Testing for PayTo Agreements

    In the Sandbox environment, it is possible to test different outcomes for PayTo agreements by altering the values of the agreement. This allows for a comprehensive evaluation of potential scenarios in a controlled setting.

    ## Use the following payment details when testing in Sandbox:

    <style type="text/css">
    .tb { table-layout:auto; width:300px;!important }
    .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
    </style>
    <table class="tb">
      <tr>
        <th>Account Name</th>
        <th>BSB</th>
        <th>Account Number</th>
        <th>PayID</th>
      </tr>
      <tr>
        <td class="td">Monoova Sample 1</td>
        <td class="td">802950</td>
        <td class="td">********</td>
        <td class="td">+61-*********</td>
      </tr>
      <tr>
        <td class="td">Monoova Sample 2</td>
        <td class="td">802950</td>
        <td class="td">********</td>
        <td class="td"><EMAIL></td>
      </tr>
      <tr>
        <td class="td">Monoova Sample 3</td>
        <td class="td">802950</td>
        <td class="td">********</td>
        <td class="td">***********</td>
      </tr>
      <tr>
        <td class="td">Monoova Sample 4</td>
        <td class="td">802950</td>
        <td class="td">********</td>
        <td class="td">*********</td>
      </tr>
    </table>

    ## Testing Payment Agreements

    Payment agreements are automatically approved or declined in the Sandbox environment, depending on the amount or `maxAmount` included in the agreement request. If both `amount` or `maxAmount` are passed, the `amount` will determine the behavior.

    For basic testing of Payment Agreements, the following test cases are recommended. A more comprehensive list of test cases is available should you wish to examine edge cases.
    </br></br>
    ### Response for Payment Agreements

    <style type="text/css">
    .tb { table-layout:auto; width:300px;!important }
    .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
    </style>
    <table class="tb">
      <tr>
        <th>Response</th>
        <th>Reason Code</th>
        <th>Contains Value</th>
        <th>Example</th>
        <th>Action</th>
      </tr>
      <tr>
        <td class="td">Agreement Approved</td>
        <td class="td">-</td>
        <td class="td">.00</td>
        <td class="td">$5.00, $10.00, $50.00</td>
        <td class="td">Approve</td>
      </tr>
      <tr>
        <td class="td">Requested By Customer</td>
        <td class="td">M019</td>
        <td class="td">.19</td>
        <td class="td">$5.19, $10.19, $50.19</td>
        <td class="td">Decline</td>
      </tr>
    </table>

    ### Other Test Cases

    <style type="text/css">
    .tb { table-layout:auto; width:300px;!important }
    .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
    </style>
    <table class="tb">
      <tr>
        <th>Response</th>
        <th>Reason Code</th>
        <th>Contains Value</th>
        <th>Example</th>
        <th>Action</th>
      </tr>
      <tr>
        <td class="td">Invalid Debtor Account Number</td>
        <td class="td">M001</td>
        <td class="td">.01</td>
        <td class="td">$5.01, $10.01, $50.01</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Closed Debtor Account Number</td>
        <td class="td">M003</td>
        <td class="td">.03</td>
        <td class="td">$5.03, $10.03, $50.03</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Blocked Account</td>
        <td class="td">M004</td>
        <td class="td">.04</td>
        <td class="td">$5.04, $10.04, $50.04</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Invalid Debtor Account Type</td>
        <td class="td">M005</td>
        <td class="td">.05</td>
        <td class="td">$5.05, $10.05, $50.05</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Transaction Forbidden</td>
        <td class="td">M006</td>
        <td class="td">.06</td>
        <td class="td">$5.06, $10.06, $50.06</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Transaction Not Supported</td>
        <td class="td">M007</td>
        <td class="td">.07</td>
        <td class="td">$5.07, $10.07, $50.07</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Not Allowed Currency</td>
        <td class="td">M008</td>
        <td class="td">.08</td>
        <td class="td">$5.08, $10.08, $50.08</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Invalid Amount</td>
        <td class="td">M009</td>
        <td class="td">.09</td>
        <td class="td">$5.09, $10.09, $50.09</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Amount Exceeds Agreed Limit</td>
        <td class="td">M010</td>
        <td class="td">.10</td>
        <td class="td">$5.10, $10.10, $50.10</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">No Mandate Service On Customer</td>
        <td class="td">M018</td>
        <td class="td">.18</td>
        <td class="td">$5.18, $10.18, $50.18</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Mandate Cancelled Due To Fraud</td>
        <td class="td">M022</td>
        <td class="td">.22</td>
        <td class="td">$5.22, $10.22, $50.22</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">No Answer From Customer</td>
        <td class="td">M027</td>
        <td class="td">.27</td>
        <td class="td">$5.27, $10.27, $50.27</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Regulatory Reason</td>
        <td class="td">M028</td>
        <td class="td">.28</td>
        <td class="td">$5.28, $10.28, $50.28</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Creditor not on Whitelist of Debtor</td>
        <td class="td">M030</td>
        <td class="td">.30</td>
        <td class="td">$5.30, $10.30, $50.30</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Creditor on Blacklist of Debtor</td>
        <td class="td">M031</td>
        <td class="td">.31</td>
        <td class="td">$5.31, $10.31, $50.31</td>
        <td class="td">Decline</td>
      </tr>
      <tr>
        <td class="td">Unknown end Customer</td>
        <td class="td">M032</td>
        <td class="td">.32</td>
        <td class="td">$5.32, $10.32, $50.32</td>
        <td class="td">Decline</td>
      </tr>
    </table>


    <br/>

    In addition to a sample request, the scheme for each endpoint and associated data tables are in the appendix at the end of this document.

    To try out our API, you can use a client such as [Postman](https://www.getpostman.com).

    You can download a postman collection of our API by clicking the link below:

    [![Run in Postman](https://run.pstmn.io/button.svg)](https://god.gw.postman.com/run-collection/19891519-fccdc471-7f26-4f4c-bba7-4e0dda5ab9e3?action=collection%2Ffork&source=rip_markdown&collection-url=entityId%3D19891519-fccdc471-7f26-4f4c-bba7-4e0dda5ab9e3%26entityType%3Dcollection%26workspaceId%3D0a304f49-2e6e-4498-8d43-3b68ee26aee6)

    **Note:** Please note we do not accept TLS 1.0 or 1.1 connections

  version: v1
  title: Monoova PayTo API

  contact:
    name: Monoova Support
    email: <EMAIL>
    url: https://www.monoova.com
  x-logo:
    url: 'https://movdpwebsiteprodae.blob.core.windows.net/images/Monoova-Primary-Logo-Black-RGB.png'
    altText: Monoova logo

tags:
  - name: Manage Payment Agreements
    description: Methods to handle the payment agreement lifecycle. There are 2 ways in which a payment agreement can be amended – unilaterally (no payer approval needed), and Bilaterally (payer approval required)

  - name: PayTo Async API
    description: |
      The `AsyncResponse` is used in operations where the request is accepted, but the processing is not immediate. This response format provides information for tracking and checking the request status.

      This API adheres to the Microsoft [async-request-reply pattern](https://learn.microsoft.com/en-us/azure/architecture/patterns/async-request-reply), ensuring efficient handling of non-immediate requests.

      Featuring high throughput and generous rate limits, this API is optimal for bulk operations, allowing for the efficient processing of larger datasets.

      </br>

      ### HTTP Status Code

      - **202 Accepted**:
        - *Description*: Indicates that the request has been accepted for processing but has not been completed.

      ### HTTP Headers

      - **Location**:
        - *Description*: Provides the URL where the client can check the status of the asynchronous operation. Clients should periodically poll this URL to retrieve the final result of the request.
        - *Example*: `https://sand-api.monoova.com/...`

      - **Retry-After**:
        - *Description*: Suggests the number of seconds the client should wait before polling the Location URL again. This helps manage server load by preventing requests that are too frequent.
        - *Example*: `10`

  - name: Initiate a Payment
    description: Once a payment agreement is created you can trigger payments against it

  - name: Notification Management
    description: All Subscriptions for mAccount

  - name: Reporting
    description: </br><h2>PayTo Reporting</h2>
      <p>
      A new report has been added specifically for NPP payments related to PayTo. Monoova’s PayTo NPP receivables – available
      <a href='https://api-docs.monoova.com/payments#operation/PayToReceivablesReportDate' target='_blank'> here </a>

      </p>
      </br>
      In addition, PayTo NPP receivables will have the following additional data fields
      </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Field </th>
      <th> Description </th>
      <th> Specification </th>
      </tr>

      <tr>
      <td class="td">

      mmsId <br/>
      </td>
      <td class="td">
      Id assigned by NPPA for payment agreement <br/>
      </td>
      <td class="td">
      string{33} <br/>
      </td>
      </tr>
      <tr>
      <td class="td">

      InitiatingPartyName <br/>
      </td>
      <td class="td">
      Initiating Party name.. <br/>
      </td>
      <td class="td">
      string{1, 35} <br/>
      </td>
      </tr>
      </table>

  - name: Webhooks
    description: <p>
      In addition to callable reporting endpoints, webhooks will also be available for state changes for payment agreements and funds received.
      </p>
      </br></br>

      <h3>Funds received notification</h3>
      <p> If Payee account is Monoova automatcher account, webhooks will be sent using a new webhook <a target='_blank' href="/payments/#operation/PayToReceivePaymentWebhook">PayToReceivePayment</a>. </p>
      </br> </br>
      <p> In addition to PayTo NPP receivables fields this webhook will have the following additional data fields to provide payment agreement information:</p>
      </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Field </th>
      <th> Description </th>
      <th> Specification </th>
      </tr>

      <tr>
      <td class="td">

      mmsId <br/>
      </td>
      <td class="td">
      Id assigned by NPPA for payment agreement <br/>
      </td>
      <td class="td">
      string{33} <br/>
      </td>
      </tr>
      <tr>
      <td class="td">

      InitiatingPartyName <br/>
      </td>
      <td class="td">
      Initiating Party name.. <br/>
      </td>
      <td class="td">
      string{1, 35} <br/>
      </td>
      </tr>
      </table>
  - name: Client Error Responses
    description: <h3> Response Structure </h3> </br>
      </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Field </th>
      <th> Description </th>
      <th> Comment </th>
      <th> Validation </th>
      </tr>
      <tr><td>	traceId	</td><td>	string 	</td><td>	traceId for the trace logs. Currently only populated for errors.	</td><td>	Optional	</td></tr>
      <tr><td>	errors[i].errorMessage	</td><td>	string 	</td><td>	detailed error messages for this error instance	</td><td>	Optional	</td></tr>
      <tr><td>	errors[i].errorCode	</td><td>	string 	</td><td>	Monoova error code	</td><td>	Mandate	</td></tr>
      <tr><td>	errors	</td><td>	Error[] 	</td><td>	array of individual errors, if any. “errors” will be null or absent of no errors occur.	</td><td>	Optional	</td></tr>
      </table>

      </br> </br>
      <h3> Sample Response </h3>
      <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <td> {</br>
      &nbsp;&nbsp; "traceId"&#58; "4ea61012-2d9b-413d-a5b4-90ad69a68741",</br>
      &nbsp;&nbsp;&nbsp;&nbsp;  "errors"&#58; [</br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  {</br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; "errorCode"&#58; "NOS_VL_EVTNAME_REQUIRED",</br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; "errorMessage"&#58; "'eventName' must not be empty."</br>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; }</br>
      &nbsp;&nbsp; ]</br>
      }</br>
      </td>
      </tr>
      </table>

  - name: Monoova Error Codes
    description: <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> HTTP Status </th>
      <th> Error Code </th>
      <th> Meaning </th>
      </tr>
      <tr><td>400</td><td>NOS_ACCTID_REQUIRED</td><td>maccount is required</td></tr>
      <tr><td>400</td><td>NOS_DATERANGE_INVALID</td><td>End must be after Start.</td></tr>
      <tr><td>400</td><td>NOS_DATERANGE_SIZE</td><td>Days between Start and End must be smaller than {0}</td></tr>
      <tr><td>400</td><td>NOS_DUP_SUB_CRT_ERROR</td><td>duplicate record already exists or subscriptionId not provided</td></tr>
      <tr><td>400</td><td>NOS_ENDDATE_INVALID</td><td>Invalid End</td></tr>
      <tr><td>400</td><td> NOS_EVTID_REQUIRED</td><td> EventId must not be empty"</td></tr>
      <tr><td>400</td><td>NOS_EVTNAME_INVALID</td><td>{0}EventName must not be empty and be one of [{1}]</td></tr>
      <tr><td>400</td><td>NOS_EVTNAME_REQUIRED</td><td>{0}eventName is required</td></tr>
      <tr><td>400</td><td>NOS_HEADER_REQUIRED</td><td>Header must not be empty</td></tr>
      <tr><td>400</td><td>NOS_ISACTIVE_REQUIRED</td><td>{0}isActive must not be empty</td></tr>
      <tr><td>400</td><td>NOS_PGNUM_INVALID</td><td>pageNumber must be greater than 0</td></tr>
      <tr><td>400</td><td>NOS_PGSIZ_INVALID</td><td>pageSize must be from 1 to {0}</td></tr>
      <tr><td>400</td><td>NOS_REQUEST_EMPTY</td><td>request payload is empty</td></tr>
      <tr><td>400</td><td>NOS_STARTDATE_INVALID</td><td>Invalid Start</td></tr>
      <tr><td>400</td><td> NOS_STS_INVALID</td><td> Status must be one of [{0}]</td></tr>
      <tr><td>400</td><td>NOS_SUBID_INVALID</td><td>SubscriptionId must be a valid Guid</td></tr>
      <tr><td>400</td><td>NOS_SUBID_REQUIRED</td><td>subscriptionId is required</td></tr>
      <tr><td>400</td><td>NOS_SUBNAME_INVALID</td><td>{0}subscriptionName must not be empty and match {RegularExpression}</td></tr>
      <tr><td>400</td><td>NOS_SUBNAME_REQUIRED</td><td>{0}subscriptionName must not be empty</td></tr>
      <tr><td>400</td><td>NOS_TRACEID_REQUIRED</td><td>traceId is required</td></tr>
      <tr><td>400</td><td>NOS_URL_INVALID</td><td>{0}callBackUrl must not be empty and must be a well-formed URL</td></tr>
      <tr><td>400</td><td>NOS_URL_REQUIRED</td><td>{0}callBackUrl is required</td></tr>
      <tr><td>400</td><td>NOS_VALIDATION_ERROR</td><td>One or more validation failures have occurred</td></tr>
      <tr><td>400</td><td>NOS_WEBDTL_REQUIRED</td><td>{0}WebHookDetail is required</td></tr>
      <tr><td>400</td><td>PAS_REQ_BODY_INVALID</td><td>Error parsing badly formed request message - {0}</td></tr>
      <tr><td>404</td><td>NOS_EVENT_NOT_FOUND_ERROR</td><td>No webhook found for&#58;{0}</td></tr>
      <tr><td>404</td><td> NOS_SUB_NOT_FOUND_ERROR</td><td> SubscriptionId {0} is not found"</td></tr>
      <tr><td>500</td><td>NOS_DEL_SUB_ERROR</td><td>Document deleted {0}. Expected exactly 1 subscription deletion.</td></tr>
      <tr><td>500</td><td>NOS_DUP_SUB_UPD_ERROR</td><td>duplicate records exist for subscriptionId {0}</td></tr>
      <tr><td>500</td><td>NOS_UNEXPECTED_ERROR</td><td>An unexpected exception occurred</td></tr>
      <tr><td>500</td><td>NOS_UPD_SUB_ERROR</td><td>Document modified {0}. Expected exactly 1 subscription updated.</td></tr>
      <tr><td>400</td><td>PAM_ACCTID_REQUIRED</td><td>maccount is required</td></tr>
      <tr><td>400</td><td>PAM_ACTION_INVALID</td><td>ActionStatus {PropertyValue} is invalid - must be one of [{0}]</td></tr>
      <tr><td>400</td><td>PAM_AMEND_FORBIDDEN</td><td>Can only amend payment agreement in active or paused status</td></tr>
      <tr><td>400</td><td>PAM_AMEND_PENDING</td><td>Can not amend payment agreement with pending bilateral amend</td></tr>
      <tr><td>400</td><td>PAM_AMOUNT_ERROR</td><td>{0}&#58; Invalid payment amount {1}</td></tr>
      <tr><td>400</td><td>PAM_AMT_GTR_MAX_AMT</td><td>Amount can not be greater than maximum amount.</td></tr>
      <tr><td>400</td><td>PAM_AMT_INVALID</td><td>{0}.amount is invalid - {PropertyValue}</td></tr>
      <tr><td>400</td><td>PAM_AMT_TYPE_INVALID_COMBINATION</td><td>For payment amount type {0}</td></tr>
      <tr><td>400</td><td> PAM_AMT_TYPE_UNSUPPORTED</td><td> Unsupported payment amount type {0}"</td></tr>
      <tr><td>400</td><td>PAM_AUTO_RENEWAL_REQUIRED</td><td>{0}.automaticRenewal is required</td></tr>
      <tr><td>400</td><td>PAM_BSB_ACCOUNT_TOGETHER</td><td>BSB and account must be provided together</td></tr>
      <tr><td>400</td><td>PAM_BUSINESS_RULE_ERROR</td><td>startdate invalid</td></tr>
      <tr><td>400</td><td>PAM_CANCEL_CREATED</td><td>Payment agreement is in created status. Please use our recall API to cancel the PAG creation</td></tr>
      <tr><td>400</td><td>PAM_CRTD_ENDDATE_INVALID</td><td>CreatedEndDate must be past time and within 1 month after CreatedStartDate</td></tr>
      <tr><td>400</td><td>PAM_CRTD_STARTDATE_INVALID</td><td>CreatedStartDate must be past time</td></tr>
      <tr><td>400</td><td>PAM_DEBTOR_PAUSED</td><td>Payment agreement paused by Debtor can only be resumed by Debtor</td></tr>
      <tr><td>400</td><td>PAM_DESC_INVALID</td><td>{0}.description must have minimum 1</td></tr>
      <tr><td>400</td><td> PAM_DESC_SHORT_DESC_MISSING</td><td> either description or shortDescription or both must be provided"</td></tr>
      <tr><td>400</td><td>PAM_DETAILS_REQUIRED</td><td>paymentDetails required</td></tr>
      <tr><td>400</td><td>PAM_DUP_PAYEE</td><td>The payee information has been provided in both the payment agreement</td></tr>
      <tr><td>400</td><td> PAM_ENDDATE_BEFORE_START</td><td> endDate cannot be earlier than paymentAgreement startDate"</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_EARLY</td><td>{0}.endDate {PropertyValue} must be later than {0}.startDate</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_FORBIDDEN</td><td>{0}.endDate is prohibited when automaticRenewal is true</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_INVALID</td><td>{0}.endDate {PropertyValue} must match yyyy-MM-dd</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_REQUIRED</td><td>{0}.endDate is required when automaticRenewal is false</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_TOO_OLD</td><td>endDate must be a future date</td></tr>
      <tr><td>400</td><td>PAM_ENDDATE_WITH_AUTORENEW</td><td>endDate cannot be provided unless automaticRenewal is false</td></tr>
      <tr><td>400</td><td>PAM_INVALID_OPERATION</td><td>PaymentAgreementStatus {0} is invalid for the change to {1}</td></tr>
      <tr><td>400</td><td>PAM_INVALID_PURPOSE</td><td>Purpose code {0} not allowed</td></tr>
      <tr><td>400</td><td>PAM_INVALID_TIME_FORMAT</td><td>{0}.ExecutionTime must match HH:MM:SS</td></tr>
      <tr><td>400</td><td>PAM_MAPPING_ERROR</td><td> Internal error - property {0} value {1} is invalid - expected one of [{2}]. Please contact Monoova support."</td></tr>
      <tr><td>400</td><td>PAM_MAX_AMT_INVALID</td><td>{0}.maximumAmount is invalid - {PropertyValue}</td></tr>
      <tr><td>400</td><td>PAM_NUM_TRANS_INVALID</td><td>{0}.NumberOfTransactionsPermitted must not be less than or equal to 0</td></tr>
      <tr><td>400</td><td>PAM_PAGID_INVALID</td><td>paymentAgreementUID {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAGID_REQUIRED</td><td>paymentAgreementUID required</td></tr>
      <tr><td>400</td><td>PAM_PAGMMSID_REQUIRED</td><td>Either PaymentAgreementUid or MmsId must not be empty</td></tr>
      <tr><td>400</td><td>PAM_PAG_EXISTS</td><td>Failed to create payment agreement - PaymentAgreementUID already linked to mmsId</td></tr>
      <tr><td>400</td><td>PAM_PAG_LIMIT_EXCEEDED</td><td>Request exceeds the maximum allowed payment agreement limit {0}</td></tr>
      <tr><td>400</td><td>PAM_PAG_PURPOSE_INVALID</td><td>{0}.purpose value {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td> PAM_PAG_STATUS_INACTIVE</td><td> Agreement status must be active"</td></tr>
      <tr><td>400</td><td>PAM_PAG_TYPE_INVALID</td><td>{0}.agreementType value {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCOUNT_NAME_FORBIDDEN</td><td>Payee account name is not allowed</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_EMPTY</td><td>{0}.payeeLinkedAccount {PropertyValue} cannot be all zeros or blanks</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_FORBIDDEN</td><td>{0}.payeeLinkedAccount prohibited with payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_INVALID</td><td>{0}.payeeLinkedAccount {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_LENGTH</td><td>{0}.payeeLinkedAccount must not be longer than 28 characters</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_MISSING</td><td>The payee account information has not been provided.</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_NAME_FORBIDDEN</td><td>{0}.payeeAccountName must be empty when payeeLinkedBsb is Monoova</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_NAME_INVALID</td><td>{0}.payeeAccountName {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_NAME_REQUIRED</td><td>{0}.payeeAccountName must not be empty</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ACCT_REQUIRED</td><td>{0}.payeeLinkedAccount required if no payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_BSB_FORBIDDEN</td><td>{0}.payeeLinkedBsb prohibited with payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_BSB_INVALID</td><td>{0}.payeeLinkedBsb {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_BSB_REQUIRED</td><td>{0}.payeeLinkedBsb required if no payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_BSB_RESTRICTED</td><td>The specified Payee bsb {0} is not allowed</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_EMPTY</td><td>{0} must contain at least one input field</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_FORBIDDEN</td><td>The account information provided does not match the list of allowed Payees.</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ID_INVALID</td><td>{0}.payeeLinkedPayId value invalid for specified payeeLinkedPayIdType</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ID_TYPE_FORBIDDEN</td><td>{0}.payeeLinkedPayIdType prohibited without payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ID_TYPE_INVALID</td><td>{0}.payeeLinkedPayIdType {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_ID_TYPE_REQUIRED</td><td>{0}.payeeLinkedPayIdType required for payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYEE_MISSING</td><td>The payee information has not been provided in either the payment agreement</td></tr>
      <tr><td>400</td><td> PAM_PAYEE_TYPE_INVALID</td><td> {0}.payeeType {PropertyValue} is invalid - must be one of [{1}]"</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ACCT_EMPTY</td><td>{0}.linkedAccount {PropertyValue} cannot be all zeros or blanks</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ACCT_FORBIDDEN</td><td>{0}.linkedAccount prohibited with linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ACCT_INVALID</td><td>{0}.linkedAccount {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ACCT_LENGTH</td><td>{0}.linkedAccount must be between 1 and 28 characters</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ACCT_REQUIRED</td><td>{0}.linkedAccount required if no linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_BSB_FORBIDDEN</td><td>{0}.linkedBsb prohibited with linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_BSB_INVALID</td><td>{0}.linkedBsb {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYER_BSB_REQUIRED</td><td>{0}.linkedBsb required if no linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_FORBIDDEN</td><td>The account information provided does not match the list of allowed Payers</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ID_INVALID</td><td>{0}.linkedPayId invalid format for linkedPayIdType</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ID_TYPE_FORBIDDEN</td><td>{0}.linkedPayIdType prohibited without linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ID_TYPE_INVALID</td><td>{0}.linkedPayIdType {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAM_PAYER_ID_TYPE_REQUIRED</td><td>{0}.linkedPayIdType required for linkedPayId</td></tr>
      <tr><td>400</td><td>PAM_PAYER_MISSING</td><td>The payer information has not been provided in either the payment agreement</td></tr>
      <tr><td>400</td><td> PAM_PAYER_NAME_INVALID</td><td> {0}.payer {PropertyValue} must match {RegularExpression}"</td></tr>
      <tr><td>400</td><td>PAM_PAYER_NAME_LENGTH</td><td>{0}.payer must be between 1 and 140 characters</td></tr>
      <tr><td>400</td><td>PAM_PAYER_NAME_REQUIRED</td><td>{0}.payer required</td></tr>
      <tr><td>400</td><td>PAM_PAYER_PARTY_ALNUM</td><td>{0}.payerPartyReference Must contain at least one alphanumeric character</td></tr>
      <tr><td>400</td><td>PAM_PAYER_PARTY_INVALID</td><td>{0}.payerPartyReference value {PropertyValue} does not match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_PAYER_PARTY_LENGTH</td><td>{0}.payerPartyReference must be from 1 to 35 characters</td></tr>
      <tr><td>400</td><td>PAM_PAYER_PARTY_REQUIRED</td><td>{0}.payerPartyReference required</td></tr>
      <tr><td>400</td><td>PAM_PAYER_PAYEE_EXISTS</td><td>Failed to create ApprovedPayerPayee - account already exists</td></tr>
      <tr><td>400</td><td>PAM_PAYER_REQUIRED</td><td>payerDetails required</td></tr>
      <tr><td>400</td><td>PAM_PAYER_TYPE_INVALID</td><td>{0}.payerType {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAM_PAYER_TYPE_REQUIRED</td><td>{0}.payerType required</td></tr>
      <tr><td>400</td><td>PAM_PAYMENTINFORMATION_FREQUENCYCOMBINATIONRULE</td><td>Either pointInTime or numberOfTransactionsPermitted must be present in paymentTerms</td></tr>
      <tr><td>400</td><td> PAM_PG_NUM_INVALID</td><td> pageNumber must be greater than zero"</td></tr>
      <tr><td>400</td><td>PAM_PG_SZ_INVALID</td><td>pageSize must be between 1 and {0}</td></tr>
      <tr><td>400</td><td>PAM_PIT_FORBIDDEN</td><td>PaymentInformation.PointInTime must not be present when PaymentFrequency is {0}</td></tr>
      <tr><td>400</td><td>PAM_PIT_INVALID</td><td>{0}.PointInTime must be 2 digits string</td></tr>
      <tr><td>400</td><td> PAM_PMT_FREQ_INVALID</td><td> {0}.paymentFrequency {PropertyValue} invalid - must be one of [{1}]"</td></tr>
      <tr><td>400</td><td>PAM_PMT_FREQ_REQUIRED</td><td>{0}.paymentFrequency is required</td></tr>
      <tr><td>400</td><td>PAM_REASON_CODE_INVALID</td><td>reasonCode {PropertyValue} is invalid - must match one of [{0}]</td></tr>
      <tr><td>400</td><td>PAM_REASON_CODE_REQUIRED</td><td>reasonCode is required when statusChange is Pause or Cancel</td></tr>
      <tr><td>400</td><td>PAM_REASON_DESC_INVALID</td><td>reasonDescription is invalid - must be from 1 to 256 characters</td></tr>
      <tr><td>400</td><td>PAM_RECALL_INVALID</td><td>Action {0} in Payment Agreement {1} invalid status - must be pending for recall</td></tr>
      <tr><td>400</td><td>PAM_RECORD_NOT_FOUND_ERROR</td><td>Requested record could not be found</td></tr>
      <tr><td>400</td><td>PAM_REQUEST_EMPTY</td><td>request payload is empty</td></tr>
      <tr><td>400</td><td>PAM_REQ_BODY_INVALID</td><td>Error parsing badly formed request message - {0}</td></tr>
      <tr><td>400</td><td>PAM_RESOLUTION_INVALID</td><td>{0}.respondByTime must be between now and 5 days in the future</td></tr>
      <tr><td>400</td><td> PAM_SCHED_INVALID</td><td> schedulePaymentInitiation not yet supported - if provided value must be false"</td></tr>
      <tr><td>400</td><td>PAM_SHORT_DESC_INVALID</td><td>{0}.shortDescription must have minimum 1</td></tr>
      <tr><td>400</td><td> PAM_STARTDATE_EARLY</td><td> {0}.startDate must be future date."</td></tr>
      <tr><td>400</td><td>PAM_STARTDATE_INVALID</td><td>{0}.startDate {PropertyValue} must match yyyy-MM-dd.</td></tr>
      <tr><td>400</td><td>PAM_STARTDATE_REQUIRED</td><td>{0}.startDate is required</td></tr>
      <tr><td>400</td><td>PAM_STATUS_CHG_INVALID</td><td>statusChange {PropertyValue} is invalid - must be one of [{0}]</td></tr>
      <tr><td>400</td><td>PAM_STATUS_CHG_REQUIRED</td><td>statusChange is required</td></tr>
      <tr><td>400</td><td>PAM_TERMS_REQUIRED</td><td>paymentTerms required</td></tr>
      <tr><td>400</td><td>PAM_TZ_INVALID</td><td>{0}.RespondByTime requires a time and timezone</td></tr>
      <tr><td>400</td><td>PAM_ULTIMATE_PAYEE_FORBIDDEN</td><td>Payee ultimate name not allowed</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYEE_FORBIDDEN</td><td>{0}.ultimatePayee must be empty when payeeLinkedBsb is Monoova</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYEE_INVALID</td><td>{0}.ultimatePayee {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYEE_REQUIRED</td><td>{0}.ultimatePayee must not be empty</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYER_INVALID</td><td>{0}.ultimatePayer {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYER_LENGTH</td><td>{0}.ultimatePayer must be between 1 and 140 characters</td></tr>
      <tr><td>400</td><td>PAM_ULT_PAYER_REQUIRED</td><td>{0}.ultimatePayer required</td></tr>
      <tr><td>400</td><td>PAM_VALIDATION_ERROR</td><td>Error validating request message - {0}</td></tr>
      <tr><td>403</td><td>PAM_FORBIDDEN_ERROR</td><td>Not allowed to {0} for account {1}</td></tr>
      <tr><td>404</td><td>PAM_ACCT_NOT_FOUND_ERROR</td><td>Account not found for AccountId {0}</td></tr>
      <tr><td>404</td><td>PAM_ACTION_NOT_FOUND_ERROR</td><td>Action {0} not found in Payment Agreement {1}</td></tr>
      <tr><td>404</td><td>PAM_PAG_FQN_NOT_FOUND_ERROR</td><td>Unable to find payment agreement with account&#58; {0} paymentAgreementUid&#58; {1} mmsId&#58; {2}</td></tr>
      <tr><td>404</td><td>PAM_PAG_NOT_FOUND_ERROR</td><td>PaymentAgreement not found for PaymentAgreementUID {0}</td></tr>
      <tr><td>404</td><td>PAM_PARTY_NOT_FOUND_ERROR</td><td>ApprovedPayerPayee with ApprovalId {0} and mAccount {1} not found</td></tr>
      <tr><td>500</td><td>PAM_ACCT_CFG_PURPOSE_ERROR</td><td>Purpose Code not provided in agreement and default value not configured in account</td></tr>
      <tr><td>500</td><td>PAM_ACCT_NOT_FOUND_ERROR</td><td>Account not found for AccountId {0}</td></tr>
      <tr><td>500</td><td>PAM_ACCT_UPDATE_ERROR</td><td>Documents modified {0}. Expected exactly 1 AccountConfiguration updated.</td></tr>
      <tr><td>500</td><td>PAM_MAPPING_ERROR</td><td>Internal error - property {0} value {1} is invalid - expected one of [{2}]. Please contact Monoova support.</td></tr>
      <tr><td>500</td><td>PAM_PAG_CREATE_ERROR</td><td>Failed to create payment agreement in repository</td></tr>
      <tr><td>500</td><td>PAM_PAG_NOT_FOUND_ERROR</td><td>PaymentAgreement not found for PaymentAgreementUID {0}</td></tr>
      <tr><td>500</td><td>PAM_PAG_UPDATE_ERROR</td><td>Documents modified {0}. Expected exactly 1 payment agreement updated.</td></tr>
      <tr><td>500</td><td>PAM_PAG_UPSERT_ERROR</td><td>Documents modified {0}. Expected 0 or 1 payment agreement updated.</td></tr>
      <tr><td>500</td><td>PAM_PARTY_CREATE_ERROR</td><td>Failed to create ApprovedPayerPayee in repository</td></tr>
      <tr><td>500</td><td>PAM_PARTY_UPDATE_ERROR</td><td>Documents modified {0}. Expected exactly 1 ApprovedPayerPayee updated.</td></tr>
      <tr><td>500</td><td>PAM_UID_NOT_UNIQUE</td><td>More than one paymentAgreementUID {0} is found</td></tr>
      <tr><td>500</td><td>PAM_UNEXPECTED_ERROR</td><td>Internal server error. Please contact Monoova support.</td></tr>
      <tr><td>502</td><td>PAM_NPP_GATEWAY_ERROR</td><td>Error returned from NPP gateway - {0}</td></tr>
      <tr><td>504</td><td>PAM_NPP_GATEWAY_TIMEOUT</td><td>Timeout from NPP gateway</td></tr>
      <tr><td>202</td><td>PAS_IN_PROGRESS_STATUS</td><td>Requested operation is still pending</td></tr>
      <tr><td>400</td><td>PAS_ACCTID_REQUIRED</td><td>maccount is required</td></tr>
      <tr><td>400</td><td>PAS_ACC_PRD_LIMIT_EXCEEDED</td><td>Payment amount requested {0} plus amount used {1} exceeds remaining account limit {2} for period.</td></tr>
      <tr><td>400</td><td>PAS_ACCT_NOT_FOUND_ERROR</td><td>Account not found for AccountId {0}.</td></tr>
      <tr><td>400</td><td>PAS_AGREEMENT_STATUS_INACTIVE</td><td>Agreement status must be active.</td></tr>
      <tr><td>400</td><td>PAS_AGREEMENT_ENDED</td><td>The end date has been exceeded on the payment agreement.</td></tr>
      <tr><td>400</td><td>PAS_AGREEMENT_INVALID</td><td>Invalid payment agreement amount or currency information. Please contact Monoova support.</td></tr>
      <tr><td>400</td><td>PAS_AGREEMENT_NOT_STARTED</td><td>The start date on the payment agreement is greater than the current date.</td></tr>
      <tr><td>400</td><td>PAS_AMOUNT_ERROR</td><td>Payment initiation amount {0} currency {1} are null or invalid.</td></tr>
      <tr><td>400</td><td>PAS_AMOUNT_MISMATCH</td><td>The amount {0} {1} does not match the active amount of the payment agreement {2} {3}.</td></tr>
      <tr><td>400</td><td>PAS_AMT_EXCEEDED</td><td>The amount {0} exceeds the active allowed amount of the payment agreement {1}.</td></tr>
      <tr><td>400</td><td>PAS_AMT_INVALID</td><td>{0}.amount is invalid - {PropertyValue}</td></tr>
      <tr><td>400</td><td>PAS_AMT_REQUIRED</td><td>{0}.amount is required</td></tr>
      <tr><td>400</td><td>PAS_BUSINESS_RULE_ERROR</td><td>Business rule failure - {0}</td></tr>
      <tr><td>400</td><td>PAS_COUNT_LIMIT_EXCEEDED</td><td>Count of payments {0} exceeds configured limits {1} for the specified payment agreement.</td></tr>
      <tr><td>400</td><td>PAS_COUNT_LIMIT_UNDEFINED</td><td>Payment count limit zero or not defined for the specified payment agreement.</td></tr>
      <tr><td>400</td><td>PAS_DEFAULT_PAYEE_ACCT_NAME_REQUIRED</td><td>{0} error&#58; Account must have DefaultPayeeAccountName configured when payee is Monoova.</td></tr>
      <tr><td>400</td><td>PAS_DUP_PAYEE</td><td>The payee information has been provided in both the payment agreement, and the current request.</td></tr>
      <tr><td>400</td><td>PAS_DUPLICATE_INSTRUCTION</td><td>Failed to create payment instruction - Duplicate paymentInitiationUID</td></tr>
      <tr><td>400</td><td>PAS_ENDDATE_EXCEEDED</td><td>createdEndDate invalid - may not be more than {0} months after createdStartDate</td></tr>
      <tr><td>400</td><td>PAS_ENDDATE_INVALID</td><td>createdEndDate invalid - must be after createdStartDate</td></tr>
      <tr><td>400</td><td>PAS_FAIL_LIMIT_EXCEEDED</td><td>Number of failed payment requests {0} exceeds limit of {1} per day.</td></tr>
      <tr><td>400</td><td>PAS_LIMIT_UNAVAILABLE</td><td>Invalid account configuration for allowed payment initiations for the day. Please contact Monoova support.</td></tr>
      <tr><td>400</td><td>PAS_MAPPING_ERROR</td><td>Internal error - property {0} value {1} is invalid - expected one of [{2}]. Please contact Monoova support.</td></tr>
      <tr><td>400</td><td>PAS_MAX_AMT_EXCEEDED</td><td>The amount {0} exceeds the allowed maximumAmount of the payment agreement {1}.</td></tr>
      <tr><td>400</td><td>PAS_PAGID_INVALID</td><td>paymentAgreementUID value {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_PAGID_REQUIRED</td><td>paymentAgreementUID required</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCTNAME_FORBIDDEN</td><td>{0}.payeeAccountName must be empty when payeeLinkedBsb is monoova</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCTNAME_INVALID</td><td>{0}.payeeAccountName {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCTNAME_LENGTH</td><td>{0}.payeeAccountName must be from 1 to 140 characters</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCTNAME_REQUIRED</td><td>{0}.payeeAccountName required</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_FORBIDDEN</td><td>{0}.payeeLinkedAccount prohibited with payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_INVALID</td><td>{0}.payeeLinkedAccount {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_LENGTH</td><td>{0}.payeeLinkedAccount must be from 1 to 28 characters</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_MISSING</td><td>The payee account information has not been provided.</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_REQUIRED</td><td>{0}.payeeLinkedAccount required if no payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCT_ZERO</td><td>{0}.payeeLinkedAccount {PropertyValue} must not be all zero or blank</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ACCTNAME_RESTRICTED</td><td>Payee account name {0} is not allowed</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_BSB_FORBIDDEN</td><td>{0}.payeeLinkedBsb prohibited with payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_BSB_INVALID</td><td>{0}.payeeLinkedBsb {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_BSB_REQUIRED</td><td>{0}.payeeLinkedBsb required if no payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_EMPTY</td><td>{0} (payeeDetails) must contain at least one input field</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_FORBIDDEN</td><td>The account information provided does not match the list of allowed Payees.</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_MISSING</td><td>The payee information has not been provided in either the payment agreement, or the current request.</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_ID_INVALID</td><td>{0}.payeeLinkedPayId invalid format for payeeLinkedPayIdType</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_PAYID_TYPE_FORBIDDEN</td><td>{0}.payeeLinkedPayIdType prohibited without payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_PAYID_TYPE_INVALID</td><td>{0}.payeeLinkedPayIdType value {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_PAYID_TYPE_REQUIRED</td><td>{0}.payeeLinkedPayIdType required for payeeLinkedPayId</td></tr>
      <tr><td>400</td><td>PAS_PAYEE_TYPE_INVALID</td><td>{0}.payeeType value {PropertyValue} invalid - must be one of [{1}]</td></tr>
      <tr><td>400</td><td>PAS_PAYMENT_TOO_SOON</td><td>The payment execute not before time has been exceeded on the payment agreement.</td></tr>
      <tr><td>400</td><td>PAS_PERIOD_COUNT_EXCEEDED</td><td>Initiation count {0} exceeds maximum allowed payment initiations for the day {1}.</td></tr>
      <tr><td>400</td><td>PAS_PMTDTL_REQUIRED</td><td>paymentDetails required</td></tr>
      <tr><td>400</td><td>PAS_PMTID_REQUIRED</td><td>paymentId is required</td></tr>
      <tr><td>400</td><td>PAS_PMT_UID_INVALID</td><td>paymentInitiationUID {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_PMT_UID_REQUIRED</td><td>paymentInitiationUID required</td></tr>
      <tr><td>400</td><td>PAS_REF_INVALID</td><td>{0}.lodgementReference invalid</td></tr>
      <tr><td>>400</td><td> PAS_REQUEST_EMPTY</td><td> request payload is empty"</td></tr>
      <tr><td>400</td><td>PAS_REQ_BODY_INVALID</td><td>Error parsing badly formed request message - {0}</td></tr>
      <tr><td>400</td><td>PAS_STARTDATE_INVALID</td><td>createdStartDate invalid - must be past date</td></tr>
      <tr><td>400</td><td>PAS_STARTDATE_REQUIRED</td><td>createdStartDate required</td></tr>
      <tr><td>400</td><td>PAS_SUPERCODE_INVALID</td><td>{0}.uniqueSuperAnnuationCode {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_SUPERCODE_REQUIRED</td><td>{0}.uniqueSuperAnnuationCode is mandatory if uniqueSuperAnnuationIdentification is given</td></tr>
      <tr><td>400</td><td>PAS_SUPERID_INVALID</td><td>{0}.uniqueSuperAnnuationIdentification {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_SUPERID_REQUIRED</td><td>{0}.uniqueSuperAnnuationIdentification is mandatory if uniqueSuperAnnuationCode is given</td></tr>
      <tr><td>400</td><td>PAS_TRNSTS_INVALID</td><td>transactionStatus value {PropertyValue} invalid - must be one of [{0}]</td></tr>
      <tr><td>400</td><td>PAS_TRNSTS_REQUIRED</td><td>transactionStatus required</td></tr>
      <tr><td>400</td><td>PAS_ULT_PAYEE_FORBIDDEN</td><td>{0}.ultimatePayee must be empty when payeeLinkedBsb is monoova</td></tr>
      <tr><td>400</td><td>PAS_ULT_PAYEE_INVALID</td><td>{0}.ultimatePayee {PropertyValue} must match {RegularExpression}</td></tr>
      <tr><td>400</td><td>PAS_ULT_PAYEE_LENGTH</td><td>{0}.ultimatePayee must be from 1 to 140 characters</td></tr>
      <tr><td>400</td><td>PAS_ULT_PAYEE_REQUIRED</td><td>{0}.ultimatePayee required</td></tr>
      <tr><td>400</td><td>PAS_ULT_PAYEE_RESTRICTED</td><td>Payee ultimate name {0} not allowed</td></tr>
      <tr><td>400</td><td>PAS_VALIDATION_ERROR</td><td>Error validating request message - {0}</td></tr>
      <tr><td>404</td><td>PAS_AGREEMENT_NOT_FOUND</td><td>The PaymentAgreement with the provided ID was not found&#58; {0}.</td></tr>
      <tr><td>404</td><td>PAS_INSTRUCTION_NOT_FOUND</td><td>PaymentInstruction {0} is not found</td></tr>
      <tr><td>500</td><td>PAS_APPLICATION_ERROR</td><td>An application error occurred while processing the request. Please try again later. If the problem persists please contact Monoova support.</td></tr>
      <tr><td>500</td><td>PAS_DATA_UPDATE_ERROR</td><td>Documents modified {0}. Expected exactly 1 payment instruction updated.</td></tr>
      <tr><td>500</td><td>PAS_IID_NOT_UNIQUE</td><td>More than one paymentInstructionUID {0} is found</td></tr>
      <tr><td>500</td><td>PAS_LIMIT_RESET_ERROR</td><td>Daily Limit Reset Request returned error - {0}</td></tr>
      <tr><td>500</td><td>PAS_MAPPING_ERROR</td><td>Internal error - property {0} value {1} is invalid - expected one of [{2}]. Please contact Monoova support.</td></tr>
      <tr><td>500</td><td>PAS_PAM_TIMEOUT_ERROR</td><td>Timeout calling MandateManager service. Please try again later.</td></tr>
      <tr><td>500</td><td>PAS_UNEXPECTED_ERROR</td><td>Internal server error. Please contact Monoova support.</td></tr>
      <tr><td>502</td><td>PAS_NPP_GATEWAY_ERROR</td><td>Error returned from NPP gateway - {0}</td></tr>
      <tr><td>502</td><td>PAS_NPP_GATEWAY_NORESPONSE</td><td>Received empty response from NPP gateway - {0}</td></tr>
      <tr><td>504</td><td>PAS_NPP_GATEWAY_TIMEOUT</td><td>Timeout calling NPP gateway</td></tr>
      <tr><td>400</td><td>MOV_UNEXPECTED_VALIDATION_FAILURE</td><td>Validation failed with banking partner. Contact Monoova for more information</td></tr>
      <tr><td>400</td><td>MOV_VALIDATION_ERROR</td><td>Unexpected validation error. Correct payload and retry</td></tr>
      <tr><td>400</td><td>MOV_PAYER_ALIAS_VALIDATION_FAILED</td><td>Unexpected validation error with Payer Alias. Correct payload and retry</td></tr>
      <tr><td>400</td><td>MOV_MULTIPLE_FIELD_CHANGE_ERROR</td><td>Unexpected validation error. Multiple changed fields exist with respondByTime. Correct request and retry</td></tr>
      <tr><td>400</td><td>MOV_MISSING_FIELD_ERROR</td><td>Unexpected validation error. Include amended fields</td></tr>
      <tr><td>400</td><td>MOV_RESPOND_BY_TIME_VALIDATION_FAILED</td><td>Unexpected validation error with respondByTime. Correct payload and retry</td></tr>
      <tr><td>400</td><td>MOV_DESCRIPTION_VALIDATION_FAILED</td><td>Unexpected validation error with descriptions. Correct payload and retry</td></tr>
      <tr><td>404</td><td>MOV_LINKED_PAYID_ERROR</td><td>Incorrect linkedPayId details</td></tr>
      <tr><td>404</td><td>MOV_PAYEE_LINKED_PAYID_ERROR</td><td>Incorrect payeeLinkedPayId details</td></tr>
      <tr><td>400</td><td>MOV_PAYEE_ALIAS_VALIDATION_FAILED</td><td>Creditor_information.account_alias_identification format incorrect for the chosen creditor_information.account_alias_type_code</td></tr>
      <tr><td>400</td><td>MOV_PAYER_ACCOUNT_UNSUPPORTED</td><td>Debtor account provided can't be used for MPS debits</td></tr>
      <tr><td>400</td><td>MOV_INVALID_BANK_ACCOUNT</td><td>The account provided does not belong to the current institution.</td></tr>
      <tr><td>408</td><td>MOV_REQUEST_TIMEOUT</td><td>Request timed out. Please try again after some time.</td></tr>
      <tr><td>400</td><td>MOV_NPP_PAYMENT_NOT_SUPPORTED</td><td>Payee account cannot receive NPP Payments</td></tr>
      <tr><td>404</td><td>MOV_PAYEE_DETAIL_ERROR</td><td>Incorrect Payee Details</td></tr>
      <tr><td>409</td><td>MOV_BANKING_PARTNER_ERROR</td><td>Unexpected error with banking partner. Contact Monoova for more details</td></tr>
      <tr><td>403</td><td>MOV_CONFIGURATION_ERROR</td><td>Configuration error. Contact Monoova</td></tr>
      <tr><td>400</td><td>MOV_AMEND_STATUS_VALIDATION_FAILED</td><td>Unexpected validation error with amend status. Correct payload and retry</td></tr>
      <tr><td>403</td><td>MOV_PERMISSION_DENIED</td><td>Configuration error. You do not have permission to perform this action. Contact Monoova</td></tr>
      <tr><td>400</td><td>MOV_INVALID_BSB</td><td>Invalid BSB number</td></tr>
      <tr><td>400</td><td>MOV_VALIDATION_FAILED</td><td>Unexpected validation error. Contact Monoova</td></tr>
      </table>

  - name: Payment Initiation Status
    description: </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Http Status Code </th>
      <th> Status </th>
      <th> Description </th>
      </tr>
      <tr><td colspan='3'><h3>Intermediate Status</h3></td> </tr>
      <tr><td>202</td><td>ACCP</td><td>Accepted Customer Profile</td></tr>
      <tr><td>202</td><td>ACSP</td><td>Accepted Settlement in Process</td></tr>
      <tr><td>202</td><td>INPR</td><td>In Progress</td></tr>
      <tr><td>202</td><td>RECV</td><td>Received</td></tr>
      <tr><td>202</td><td>SAFD</td><td>Participant Down</td></tr>
      <tr><td>202</td><td>SENT</td><td>Sent To Payer</td></tr>
      <tr><td>202</td><td>UNDV</td><td>Undelivered</td></tr>
      <tr><td colspan='3'><h3>Final Status</h3></td> </tr>
      <tr><td>	201	</td><td>	ACSC	</td><td>	Accepted Settlement Completed	</td></tr>
      <tr><td>	422	</td><td>	RJCT 	</td><td>	Rejected 	</td></tr>
      </table>
      </br> </br>

  - name: Payment Agreement Status
    description: </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th>Status</th>
      <th>Status Description</th>
      </tr>
      <tr><td>InProgress</td><td>Creation In Progress</td></tr>
      <tr><td>Created</td><td>Pending Payer Approval</td></tr>
      <tr><td>Active</td><td>Active</td></tr>
      <tr><td>Paused</td><td>Paused</td></tr>
      <tr><td>Cancelled</td><td>Cancelled</td></tr>
      <tr><td>Rejected</td><td>Creation Rejected</td></tr>
      <tr><td>Expired</td><td>Creation Timed Out</td></tr>
      </table>
      </br> </br>

  - name: Payment Initiation Reject Reason Codes
    description: </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Code </th>
      <th> Description </th>
      </tr>
      <tr><td>	R001	</td><td>	Creditor agent is not online	</td></tr>
      <tr><td>	R002	</td><td>	Account to be debited does not exist	</td></tr>
      <tr><td>	R003	</td><td>	Account to be Credited does not exist	</td></tr>
      <tr><td>	R004	</td><td>	The original Payer Customer Account number is closed	</td></tr>
      <tr><td>	R005	</td><td>	Account exists but is blocked	</td></tr>
      <tr><td>	R006	</td><td>	Account is now closed	</td></tr>
      <tr><td>	R007	</td><td>	Debtor account type is invalid	</td></tr>
      <tr><td>	R008	</td><td>	Creditor account type is invalid	</td></tr>
      <tr><td>	R009	</td><td>	Payer account is not valid	</td></tr>
      <tr><td>	R010	</td><td>	Transaction has been forbidden	</td></tr>
      <tr><td>	R011	</td><td>	Payment was rejected by the payee	</td></tr>
      <tr><td>	R012	</td><td>	Debtor account cannot be debited	</td></tr>
      <tr><td>	R013	</td><td>	Use of zero-dollar payment initiation requests is prohibited.	</td></tr>
      <tr><td>	R014	</td><td>	The amount requested is greater than the maximum NPP limit of $99,999,999,999	</td></tr>
      <tr><td>	R015	</td><td>	Specified message amount is a non-processable currency outside of existing agreement	</td></tr>
      <tr><td>	R016	</td><td>	Amount of funds available to cover specified message amount is insufficient	</td></tr>
      <tr><td>	R017	</td><td>	Specified transaction amount is less than agreed minimum.	</td></tr>
      <tr><td>	R018	</td><td>	Amount received is not the amount agreed or expected	</td></tr>
      <tr><td>	R019	</td><td>	The amount in the NPP Payment Initiation Request is missing or invalid	</td></tr>
      <tr><td>	R020	</td><td>	Number of transactions at the Group level is invalid or missing	</td></tr>
      <tr><td>	R021	</td><td>	The amount requested in the NPP Payment Initiation Request exceeds the agreed limit	</td></tr>
      <tr><td>	R022	</td><td>	Reject the NPP Payment Initiation Request as the Creditor is unknown to Debtor	</td></tr>
      <tr><td>	R023	</td><td>	End customer specified is not known at associated Sort/National Bank Code or does no longer exist in the books	</td></tr>
      <tr><td>	R024	</td><td>	Debtor Name not provided	</td></tr>
      <tr><td>	R025	</td><td>	Creditor Name not provided	</td></tr>
      <tr><td>	R026	</td><td>	Number of decimal points not compatible with the currency	</td></tr>
      <tr><td>	R027	</td><td>	Required Compulsory Element Missing	</td></tr>
      <tr><td>	R028	</td><td>	The currency included in the Clearing Request is incorrect (value other than AUD).	</td></tr>
      <tr><td>	R029	</td><td>	Cancellation requested by the Debtor	</td></tr>
      <tr><td>	R030	</td><td>	The CreationDateTime in the Group Header is not as per the required format	</td></tr>
      <tr><td>	R031	</td><td>	The Business Service does not support future dated NPP Payment Initiation Requests	</td></tr>
      <tr><td>	R032	</td><td>	Check with Monoova on possible Outage. Retry again after sometime.	</td></tr>
      <tr><td>	R033	</td><td>	Check with Monoova on possible Outage. Retry again after sometime.	</td></tr>
      <tr><td>	R034	</td><td>	End to End Id missing or invalid for catsct payment Instruction.	</td></tr>
      <tr><td>	R035	</td><td>	Invalid or not applicable character set	</td></tr>
      <tr><td>	R036	</td><td>	Creditor Reference Must be equal to End to End Id of payment instruction for catsct	</td></tr>
      <tr><td>	R037	</td><td>	Check with Monoova on possible Outage. Retry again after sometime.	</td></tr>
      <tr><td>	R038	</td><td>	Payer institution is unavailable	</td></tr>
      <tr><td>	R039	</td><td>	Payer institution is unavailable	</td></tr>
      <tr><td>	R040	</td><td>	Payer institution is unavailable	</td></tr>
      <tr><td>	R041	</td><td>	Payer PayId is not valid. Update the payment agreement with a valid PayId to proceed with payment initiation	</td></tr>
      <tr><td>	R042	</td><td>	Payer PayId is not valid. Update the payment agreement with a valid PayId to proceed with payment initiation	</td></tr>
      <tr><td>	R043	</td><td>	Payer BSB is not valid. Update the payment agreement with a valid BSB to proceed with payment initiation	</td></tr>
      <tr><td>	R044	</td><td>	Payer BSB is not valid. Update the payment agreement with a valid BSB to proceed with payment initiation	</td></tr>
      <tr><td>	R045	</td><td>	BO Service Code is not valid. Modify the Mandate with the correct BO Service Code	</td></tr>
      <tr><td>	R046	</td><td>	BO Service Code is not valid. Modify the Mandate with the correct BO Service Code	</td></tr>
      <tr><td>	R047	</td><td>	Payer is no longer reachable on NPP. Cancel the Mandate	</td></tr>
      <tr><td>	R048	</td><td>	Payer is no longer reachable on NPP. Cancel the Mandate	</td></tr>
      <tr><td>	R049	</td><td>	Payee Account Details are not present in Mandate and Client also has not provided Creditor Account Details in the Initiation Request. Either Modify the Mandate or provide the Creditor Account Details in the Request	</td></tr>
      <tr><td>	R050	</td><td>	Creditor PayId has been ported, amend the payment agreement to reflect correct PayId institution	</td></tr>
      <tr><td>	R051	</td><td>	Payer PayId has been ported, amend the payment agreement to reflect correct PayId institution	</td></tr>
      <tr><td>	R052	</td><td>	Payee account details in the payment initiation do not match the payment agreement	</td></tr>
      <tr><td>	R053	</td><td>	Payee PayId in the payment initiation do not match the payment agreement	</td></tr>
      <tr><td>	R054	</td><td>	Verify the details of the Mandate, if creditor details are not present, it must be provided in input request	</td></tr>
      <tr><td>	R055	</td><td>	Unable to locate Payment Instruction record	</td></tr>
      <tr><td>	R056	</td><td>	Mandate validation failed. Verify the details of the Mandate	</td></tr>
      <tr><td>	R057	</td><td>	Verify the details of the Mandate, if creditor account/alias details are correct, don't send creditor account/alias details in input request	</td></tr>
      <tr><td>	R058	</td><td>	Verify the status of previous payment instruction if it is valid for business retry	</td></tr>
      <tr><td>	R059	</td><td>	The NPP Payment Initiation Request did not contain a MandateId	</td></tr>
      <tr><td>	R060	</td><td>	Invalid Payment Agreement	</td></tr>
      <tr><td>	R061	</td><td>	Payment Agreement is expired	</td></tr>
      <tr><td>	R062	</td><td>	Reason has not been specified by end customer	</td></tr>
      <tr><td>	R063	</td><td>	Permission to process this payment is not granted	</td></tr>
      <tr><td>	R064	</td><td>	Payer institution is unavailable. Please try again later.	</td></tr>
      <tr><td>	R065	</td><td>	The BIC identifier in the Message Payload is invalid or missing	</td></tr>
      <tr><td>	R066	</td><td>	Due to specific service offered by the Debtor Agent	</td></tr>
      <tr><td>	R067	</td><td>	The Creditor did not appear on the Debtors whitelist	</td></tr>
      <tr><td>	R068	</td><td>	The Creditor did appear on the Debtors blacklist	</td></tr>
      <tr><td>	R069	</td><td>	The NPP Payment Initiation Request was rejected because the number of transactions requested exceeds the Debtor Agent offering.	</td></tr>
      <tr><td>	R070	</td><td>	The NPP Payment Initiation Request was rejected because the total value of transactions requested exceeds the Debtor Agent offering.	</td></tr>
      <tr><td>	R999	</td><td>	Unexpected System Error	</td></tr>

      </table>

  - name: Mandate Status Reason Codes
    description: </br> <style type="text/css">
      .tb { table-layout:auto; width:300px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr>
      <th> Code </th>
      <th> Description </th>
      </tr>
      <tr><td>	M001	</td><td>	Invalid Debtor AccountNumber	</td></tr>
      <tr><td>	M002	</td><td>	Closed Account Number	</td></tr>
      <tr><td>	M003	</td><td>	Closed Debtor AccountNumber	</td></tr>
      <tr><td>	M004	</td><td>	Blocked Account	</td></tr>
      <tr><td>	M005	</td><td>	Invalid Debtor AccountType	</td></tr>
      <tr><td>	M006	</td><td>	Transaction Forbidden	</td></tr>
      <tr><td>	M007	</td><td>	Transaction Not Supported	</td></tr>
      <tr><td>	M008	</td><td>	Not Allowed Currency	</td></tr>
      <tr><td>	M009	</td><td>	Invalid Amount	</td></tr>
      <tr><td>	M010	</td><td>	Amount Exceeds Agreed Limit	</td></tr>
      <tr><td>	M011	</td><td>	Contract Amended	</td></tr>
      <tr><td>	M012	</td><td>	Contract Cancellation Initiated By Debtor	</td></tr>
      <tr><td>	M013	</td><td>	Contract Expired	</td></tr>
      <tr><td>	M014	</td><td>	Mandate Suspended Final Collection	</td></tr>
      <tr><td>	M015	</td><td>	Mandate Suspended Once Off Collection	</td></tr>
      <tr><td>	M016	</td><td>	End Customer Deceased	</td></tr>
      <tr><td>	M017	</td><td>	No Mandate Service By Agent	</td></tr>
      <tr><td>	M018	</td><td>	No Mandate Service On Customer	</td></tr>
      <tr><td>	M019	</td><td>	Requested By Customer	</td></tr>
      <tr><td>	M020	</td><td>	Requested By Initiating Party	</td></tr>
      <tr><td>	M021	</td><td>	Mandate Expired	</td></tr>
      <tr><td>	M022	</td><td>	Mandate Cancelled Due To Fraud	</td></tr>
      <tr><td>	M023	</td><td>	Not Specified Reason Customer Generated	</td></tr>
      <tr><td>	M024	</td><td>	Not Specified Reason Agent Generated	</td></tr>
      <tr><td>	M025	</td><td>	Mandate Suspended 7 Consecutive Unsuccessful Collections	</td></tr>
      <tr><td>	M026	</td><td>	Narrative	</td></tr>
      <tr><td>	M027	</td><td>	No Answer From Customer	</td></tr>
      <tr><td>	M028	</td><td>	Regulatory Reason	</td></tr>
      <tr><td>	M029	</td><td>	Specific Service offered by Debtor Agent	</td></tr>
      <tr><td>	M030	</td><td>	Creditor not on Whitelist of Debtor	</td></tr>
      <tr><td>	M031	</td><td>	Creditor on Blacklist of Debtor	</td></tr>
      <tr><td>	M032	</td><td>	Unknown end Customer	</td></tr>

      </table>

      </br> </br>

  - name: 'PAYID Format Validation'
    description: <style type="text/css">
      .tb { table-layout:auto; width:100px;!important }
      .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
      </style>
      <table class="tb">
      <tr><td> <b>PhoneNumber (mapped to TELI)</b> </td><td>	Consists of a "+" followed by the country code (from 1 to 3 characters) then a "-" then a number between 1-9 and then any combination of numbers between 0-9. Total of up to 35 characters. </br>Format pattern&#58; ^[+][0-9]{1,3}-[1-9]{1,1}[0-9]{1,29}$ </td></tr>
      <tr><td> <b>Email (mapped to EMAL)</b> </td><td>	Consists of a character string with a maximum length of 256 characters in lower case. This must include the "@" symbol with leading and trailing characters and no white spaces. </br>Format pattern&#58; (MAX256 text) ^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)$	</td></tr>
      <tr><td> <b>ACN/ABN (mapped to AUBN)</b> </td><td>	Consists of a nine to eleven digit number where the first two digits are a checksum. This is assigned by the Australian Taxation Office to identify an individual Australian Business.
      <br>Format pattern&#58; (MIN9 - MAX11 digit). ^((\d{9})|(\d{11}))$ </br>
      <br>Note&#58; This field will support both ABN and ACN</br>
      ACN= 9 digits </br>
      ABN = 11 digits
      </td></tr>
      <tr><td> <b>OrganisationId (mapped to ORGN)</b> </td><td>	The Identifier must include the company/organisation name and both/either the description of the business/ trade / product / campaign and/or geographic location.
      <br>Format pattern &#58; Maximum of 256 characters in lower case, to be drawn from the NPP Character Set and without leading or trailing whitespace.<br>
      <br>^[!-@[-~][ -@[-~]{0,254}[!-@[-~]$ <br>
      </td></tr>
      </table>

x-tagGroups:
  - name: ''
    tags:
      - Generate a Bearer Token
      - Manage Payment Agreements
      - PayTo Async API
      - Initiate a Payment
      - Reporting
      - Webhooks
      - Notification Management
      - PAYID Format Validation
      - Client Error Responses
      - Monoova Error Codes
      - Mandate Status Reason Codes
      - Payment Initiation Status
      - Payment Agreement Status
      - Payment Initiation Reject Reason Codes

security:
  - BearerAuth: []

paths:
  ## PayTo
  # Payment Agreement
  /au/payto/pam-v1/PaymentAgreement:
    post:
      tags:
        - Manage Payment Agreements
      summary: Create a Payment Agreement
      description:
        The purpose of this endpoint is to create a payment agreement. An agreement can only initiate payments once the Payer approves it. Payers must respond by the respondByTime specified while creating the mandate. If respondByTime is not provided, it will be defaulted to 5 days for the Payer to approve or decline a payment agreement.</br>
        </br>
        <strong>Rate Limit</strong></br>
        Payment Agreement and Initiation endpoints have rate limits applied at a service level. If you process at volumes that surpass the rate limits, you will get a 429 HTTP status code for 'too many requests'. If you see this error, you will need to retry. If you experience this issue frequently, space out the calls. Alternatively, the Async endpoints have a much higher rate limit applied.
        </br></br>
        A webhook will notify customers about the approval/declination of a payment agreement.
        There is also get payment agreement endpoints to check the status of the payment agreement.
        <br/><br/> To receive payments in an account with Monoova, please <a href='https://api-docs.monoova.com/payments#operation/ReceivablesCreate' taget='_blank'>create an automatcher account</a> and use it under payee details when creating payment agreement. Automatcher accounts can also be created through the Sandbox Portal. Received payments through an automatcher account will be reported via webhook notification event name <a href="https://api-docs.monoova.com/payments#tag/Webhooks/operation/PayToReceivePaymentWebhook" target="_blank">“PayToReceivePayment”</a>.

        <br/>Additionally, sample test accounts will need to be used for the payer-consumer side of the mandate on Sandbox. Please find below sample test payer accounts&#58;
        <style type="text/css">
        .tb { table-layout:auto; width:100px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">
        <tr>
        <th> Account Name </th>
        <th> Monoova Sample 1 </th>
        <th> Monoova Sample 2 </th>
        <th> Monoova Sample 3 </th>
        <th> Monoova Sample 4 </th>
        <th> Monoova Sample 5 </th>
        </tr>
        <tr><td>	BSB	</td><td>	802950	</td><td>		</td><td>		</td><td>		</td><td>		</td></tr>
        <tr><td>	Account Number	</td><td>	********	</td><td>		</td><td>		</td><td>		</td><td>		</td></tr>
        <tr><td>	PayID - Phone	</td><td>		</td><td>	+61-*********	</td><td>		</td><td>		</td><td>		</td></tr>
        <tr><td>	PayID - Email	</td><td>		</td><td>		</td><td>	<EMAIL>	</td><td>		</td><td>		</td></tr>
        <tr><td>	PayID - ABN	</td><td>		</td><td>		</td><td>		</td><td>	***********	</td><td>		</td></tr>
        <tr><td>	PayID - Organisation ID	</td><td>		</td><td>		</td><td>		</td><td>		</td><td>	*********	</td></tr>

        </table>

        </strong>
        </br> </br> Below are a series of reference tables for the create and initiate payment agreement schemas
        </br>
        </br>
        <h3>Frequency</h3>
        <style type="text/css">
        .tb { table-layout:auto; width:100px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">
        <tr>
        <th> Code </th>
        <th> Name </th>
        <th> Description </th>
        </tr>
        <tr><td>	ADHO	</td><td>	Adhoc	</td><td>	Event takes place on request or as necessary.	</td></tr>
        <tr><td>	DAIL	</td><td>	Daily	</td><td>	Event takes place every day.	</td></tr>
        <tr><td>	FRTN	</td><td>	Fortnightly	</td><td>	Event takes place every two weeks.	</td></tr>
        <tr><td>	INDA	</td><td>	IntraDay	</td><td>	Event takes place several times a day.	</td></tr>
        <tr><td>	MIAN	</td><td>	SemiAnnual	</td><td>	Event takes place every six months or two times a year.	</td></tr>
        <tr><td>	MNTH	</td><td>	Monthly	</td><td>	Event takes place every month or once a month.	</td></tr>
        <tr><td>	QURT	</td><td>	Quarterly	</td><td>	Event takes place every three months or four times a year.	</td></tr>
        <tr><td>	WEEK	</td><td>	Weekly	</td><td>	Event takes place once a week.	</td></tr>
        <tr><td>	YEAR	</td><td>	Annual	</td><td>	Event takes place every year or once a year.	</td></tr>


        </table>

        </br>
        </br>
        <h3>Agreement Type</h3>
        <style type="text/css">
        .tb { table-layout:auto; width:200px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">
        <tr>
        <th> Code </th>
        <th> Name </th>
        <th> Description </th>
        </tr>
        <tr><td>	FIXE	</td><td>	Fixed	</td><td>	Payment amount is fixed.	</td></tr>
        <tr><td>	USGB	</td><td>	UsageBased	</td><td>	Payment amount is based on usage.	</td></tr>
        <tr><td>	VARI	</td><td>	Variable	</td><td>	Payment amount is variable.	</td></tr>
        </table>
        </br>
        </br>
        <h3>Agreement Type + Amount</h3>
        <style type="text/css">
        .tb { table-layout:auto; width:100px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">
        <tr>
        <th> agreementType </th>
        <th> amount </th>
        <th> maximumAmount </th>
        <th> Rules </th>
        </tr>
        <tr><td>	FIXE	</td><td>	Valid numeric value	</td><td>	NA	</td><td>	If Fixed, only amount should have values. Max amount should not be present	</td></tr>
        <tr><td>	USGB	</td><td>	Valid numeric value	</td><td>	Valid numeric value	</td><td>	If Usage based, max amount is recommended. However, amount and max amount are optional. If both amount and max amount are present, amount should be less than max amount	</td></tr>
        <tr><td>	VARI	</td><td>	Valid numeric value	</td><td>	Valid numeric value	</td><td>	If Variable, max amount is recommended. However, amount and max amount are optional. If both amount and max amount are present, amount should be less than max amount	</td></tr>
        </table>
      operationId: post-paymentagreement
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentAgreementDto'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentAgreementResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

    get:
      tags:
        - Reporting
      summary: Get Payment Agreements by Date Range
      description: Previously created Payment Agreements can be retrieved individually date range.
      operationId: get-paymentagreement
      parameters:
        - name: createdStartDate
          in: query
          required: true
          description: Start Date. </br>Required. ISO8601 date-time
          schema:
            type: string
            format: date-time
        - name: pageNumber
          in: query
          description: Page to be returned after records are split as per pageSize. <br/> Optional, If not provided, it is defaulted to 1.
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: Number of records per page. </br> Optional, If not provided page size will be defaulted to 50. Maximum allowed value is 200.
          schema:
            type: integer
            format: int32
        - name: createdEndDate
          in: query
          description: End Date. Optional, if not provided it will be defaulted to createdStartDate +24 hours. ISO8601 date-time
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentAgreementsListResponse'

        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/paymentagreement/{paymentAgreementUID}/unilateral':
    patch:
      tags:
        - Manage Payment Agreements
      summary: Unilateral Amendment
      description: Unilateral changes to a payment agreement can be done without the payer’s approval. The only fields that can be changed unilaterally are the payee object and agreement descriptions.
      operationId: patch-paymentagreement-id-unilateral
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            description: Id to uniquely Identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            example: MONPAG12345
            nullable: true
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/AmendUnilateralDetailsDto'
            example:
              description: payroll pag1
              shortDescription: payroll pag1
              payeeDetails:
                payeeType: ORGN
                payeeLinkedBsb: 802980
                payeeLinkedAccount: ********
                payeeLinkedPayId: <EMAIL>
                payeeLinkedPayIdType: EMAIL
                payeeAccountName: PayCo
                ultimatePayee: PayCo

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AmendUnilateralDetailsResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'b008558d-b6df-4935-b2aa-39b2f8d467be',
                  'errors':
                    [
                      {
                        'errorCode': 'PAM_BR_AMEND_FORBIDDEN',
                        'errorMessage': 'Can only amend payment agreement in active or paused status'
                      }
                    ]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/paymentagreement/{paymentAgreementUID}/bilateral':
    patch:
      tags:
        - Manage Payment Agreements
      summary: Bilateral Amendment
      operationId: patch-paymentagreement-id-bilateral
      description: Bilateral changes can't be done without the payer’s approval. Changes can be made to payment terms. Changes made using this end point will only get reflected after payer approves the change.  Until new terms are approved, payment agreement will be active with its last approved terms.
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            description: Id to uniquely Identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            example: MONPAG12345
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AmendBilateralDetailsDto'
            example:
              automaticRenewal: false
              endDate: '2022-09-30'
              respondByTime: '2022-09-1T08:20:50.52Z'
              paymentTerms:
                numberOfTransactionsPermitted: 10
                frequency: MNTH
                amount: 10.00
                maximumAmount": 1000.00
                agreementType: VARI

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AmendBilateralDetailsResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/paymentagreement/{paymentAgreementUID}/status':
    patch:
      tags:
        - Manage Payment Agreements
      summary: Amend Payment Agreement Status
      description: |
        This endpoint allows the initiator to change the status of a payment agreement. Available statuses are `active`, `pause`, or `cancel`.</br>
        </br>
        <h3>Status Reason Code</h3>
        <style type="text/css">
        .tb { table-layout:auto; width:200px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">
        <tr><th> Code </th><th> Reason </th></tr>
        <tr><td>AC02</td><td>Invalid Debtor Account Number</td></tr>
        <tr><td>AC04</td><td>Closed Account Number</td></tr>
        <tr><td>AC05</td><td>Closed Debtor Account Number</td></tr>
        <tr><td>AC06</td><td>Blocked Account</td></tr>
        <tr><td>AC13</td><td>Invalid Debtor Account Type</td></tr>
        <tr><td>AG01</td><td>Transaction Forbidden</td></tr>
        <tr><td>AG03</td><td>Transaction Not Supported</td></tr>
        <tr><td>AM03</td><td>Not Allowed Currency</td></tr>
        <tr><td>AM12</td><td>Invalid Amount</td></tr>
        <tr><td>AM14</td><td>Amount Exceeds Agreed Limit</td></tr>
        <tr><td>CTAM</td><td>Contract Amended</td></tr>
        <tr><td>CTCA</td><td>Contract Cancellation Initiated By Debtor</td></tr>
        <tr><td>CTEX</td><td>Contract Expired</td></tr>
        <tr><td>MCFC</td><td>Payment Agreement Suspended Final Collection</td></tr>
        <tr><td>MCOC</td><td>Payment Agreement Suspended Once Off Collection</td></tr>
        <tr><td>MD07</td><td>End Customer Deceased</td></tr>
        <tr><td>MD08</td><td>No Payment Agreement Service By Agent</td></tr>
        <tr><td>MD09</td><td>No Payment Agreement Service On Customer</td></tr>
        <tr><td>MD16</td><td>Requested By Customer</td></tr>
        <tr><td>MD17</td><td>Requested By Initiating Party</td></tr>
        <tr><td>MD20</td><td>Payment Agreement Expired</td></tr>
        <tr><td>MD21</td><td>Payment Agreement Cancelled Due To Fraud</td></tr>
        <tr><td>MS02</td><td>Not Specified Reason Customer Generated</td></tr>
        <tr><td>MS03</td><td>Not Specified Reason Agent Generated</td></tr>
        <tr><td>MSUC</td><td>Payment Agreement Suspended 7 Consecutive Unsuccessful Collections</td></tr>
        <tr><td>NARR</td><td>Narrative</td></tr>
        <tr><td>NOAS</td><td>No Answer From Customer</td></tr>
        <tr><td>RR04</td><td>Regulatory Reason</td></tr>
        <tr><td>SL01</td><td>Specific Service Offered By Debtor Agent</td></tr>
        <tr><td>SL11</td><td>Creditor Not On Whitelist Of Debtor</td></tr>
        <tr><td>SL12</td><td>Creditor On Blacklist Of Debtor</td></tr>
        </table>
      operationId: patch-paymentagreement-id-status
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            description: Id to uniquely identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]{1,35}$
            example: MONPAG12345
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AmendPaymentAgreementStatusDto'
            example:
              statusChange: pause
              reasonCode: AC02
              reasonDescription: Amend mandate status

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AmendPaymentAgreementStatusResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  # Recall
  '/au/payto/pam-v1/paymentAgreement/{PaymentAgreementUID}/recall/{actionIdentification}':
    patch:
      tags:
        - Manage Payment Agreements
      summary: Recall a Payment Agreement
      description: The purpose of this operation is to recall a pending payment agreement before it is approved or declined by the payer servicer/customer.
      operationId: patch-paymentagreement-id-recall
      parameters:
        - name: PaymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            description: Id to uniquely Identify a payment agreement.
            example: MONPAG12345
        - name: actionIdentification
          in: path
          required: true
          schema:
            type: string
            maxLength: 35
            pattern: ^[a-f0-9]{12}1[a-f0-9]{3}[89ab][a-f0-9]{15}$
            description: unique Id used to identify pending(unapproved) actions. This id is assigned when a new payment agreement is created or when a bilateral amendment is made.
            example: 6ae818a80bc362618aeaece46ee70e4a
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecallPaymentAgreementLastActionResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/paymentAgreement/{paymentAgreementUID}':
    get:
      tags:
        - Reporting
      summary: 'Get Payment Agreement by UniqueId'
      operationId: get-paymentagreement-id
      description: Previously created Payment Agreements can be retrieved individually via uniqueId
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentAgreementResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors':
                    [
                      {
                        'errorCode': 'PAM_PAG_NOT_FOUND_ERROR',
                        'errorMessage': 'PaymentAgreement not found for PaymentAgreementUID 12345'
                      }
                    ]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  # Payment Initiation
  /au/payto/pas-v1/paymentInstruction:
    post:
      tags:
        - Initiate a Payment
      summary: Payment Instruction
      description: Once a valid payment agreement has approved by the payer this endpoint is used to create a payment initiation, which creates real time pull payments.<br/> You are allowed a maximum of 5 failed initiations in a 24 hour period, if you reach this limit you will need to wait until the next day to retry.
      operationId: post-paymentinstruction
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateMandatePaymentDto'
            example:
              paymentAgreementUID: BCORP123456
              paymentInitiationUID: BCORP00000001
              paymentDetails:
                amount: 1.00
                lodgementReference: 'BCorp PIR 001'

      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitiateMandatePaymentResponseDto'
        '202':
          description: pending
          content:
            application/json:
              example:
                {
                  paymentAgreementUID: '**********',
                  paymentInitiationUID: 'C150D6FAE2044CA9A3ADB470874E8F86',
                  mmsId: 'ed8569b4fd8614259d13bcb9a2da2bc0',
                  nppInstructionId: 'MOPYAUS1XXXI20241119004756058205270',
                  paymentInitiationStatus: 'INPR',
                  paymentInitiationStatusDescription: 'In progress',
                  traceId: 'f3ae20c5-e400-4eab-8fb0-16e03c8481be'
                }
        '422':
          description: Rejected
          content:
            application/json:
              example:
                {
                  paymentAgreementUID: '**********',
                  paymentInitiationUID: 'C150D6FAE2044CA9A3ADB470874E8F86',
                  mmsId: 'ed8469b4fd8616259d15b4b9a2da2bc0',
                  paymentInitiationStatus: 'RJCT',
                  paymentInitiationStatusDescription: 'Rejected',
                  statusReasonCode: 'R999',
                  statusReasonDescription: 'Payment initiation rejected by banking institution. Please contact Monoova',
                  traceId: 'f3ae20c5-e400-4eab-8fb0-16e03c8481be'
                }
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

    get:
      tags:
        - Reporting
      summary: Get Payment Instructions By Date Range
      description: Previously created Payment initiations can be retrieved individually via date range.
      operationId: get-paymentinstruction
      parameters:
        - name: createdStartDate
          in: query
          description: Required. Format - ISO8601 date-time.
          schema:
            type: string
            format: date-time
        - name: pageNumber
          description: Number of records per page </br> Optional, If not provided, value will be defaulted to 1.
          in: query
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: Number of records per page. </br> Optional, If not provided page size will be defaulted to 50. Maximum allowed value is 200.
          schema:
            type: integer
            format: int32
        - name: createdEndDate
          in: query
          description: Start Date. </br>Optional, if not provided it will be defaulted to createdStartDate.  ISO8601 date-time
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  paymentInitiationDetails:
                    type: array
                    items:
                      $ref: '#/components/schemas/PaymentStatusResponseDto'
                    nullable: true
                  traceId:
                    type: string
                    nullable: true
                    example: 7fcb7dc4-ab8d-4bc8-8fa8-1335172377e2
                additionalProperties: false
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors':
                    [
                      {
                        'errorCode': 'string',
                        'errorMessage': 'PaymentInstruction 12345 is not found'
                      }
                    ]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pas-v1/paymentinstruction/{paymentInitiationUID}':
    get:
      tags:
        - Reporting
      summary: 'Get Payment Instruction By UniqueId'
      description: Previously created Payment initiations can be retrieved individually via uniqueId.
      operationId: get-paymentinstruction-paymentinitiationid
      parameters:
        - name: paymentInitiationUID
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  paymentInitiationDetail:
                    $ref: '#/components/schemas/PaymentStatusResponseDto'
                  traceId:
                    type: string
                    nullable: true
                    example: 7fcb7dc4-ab8d-4bc8-8fa8-1335172377e2
                additionalProperties: false
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors':
                    [
                      {
                        'errorCode': 'PAS_INSTRUCTION_NOT_FOUND',
                        'errorMessage': 'PaymentInstruction 12345 is not found'
                      }
                    ]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  #Async APIs
  /au/payto/pam-v1/PaymentAgreementAsync/Request/{uniqueRequestId}:
    get:
      tags:
        - PayTo Async API
      summary: Get Async Payment Agreement Request Status
      description: >
        Queries the status of an asynchronous API request for creating a payment agreement. This endpoint requires the {uniqueRequestId} returned by the original Async API call. There is no request body.
      operationId: get-async-paymentagreement-status
      parameters:
        - name: uniqueRequestId
          in: path
          required: true
          schema:
            type: string
            description: Unique identifier returned by the original Async API call.
            example: '4834dba5-27b0-465c-aea7-aa530315c68c'
      responses:
        '200':
          description: Request is still processing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PendingAgreementAsyncResponse'
        '201':
          description: Successful completion. The Location header contains the URL for the updated resource/document.
          headers:
            Location:
              schema:
                type: string
              description: URL to be polled for the updated resource/document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessAgreementAsyncResponse'
        '400':
          description: Invalid status request.
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '422':
          description: Background job has failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncMandateRequestFailedResponse'

  /au/payto/pas-v1/PaymentInstructionAsync/Request/{uniqueRequestId}:
    get:
      tags:
        - PayTo Async API
      summary: Get Async Payment Instruction Request Status
      description: >
        Queries the status of an asynchronous API request, such as "Initiate Payment Async". This endpoint requires the {uniqueRequestId} returned by the original Async API call. No request body is needed.
      operationId: get-async-paymentinitiation-status
      parameters:
        - name: uniqueRequestId
          in: path
          required: true
          schema:
            type: string
            description: Unique identifier returned by the original Async API call.
            example: '4834dba5-27b0-465c-aea7-aa530315c68c'
      responses:
        '200':
          description: Request is still processing.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PendingInitiationAsyncResponse'
        '201':
          description: Successful completion. Location header contains the URL for the updated resource/document.
          headers:
            Location:
              schema:
                type: string
              description: URL to be polled for the updated resource/document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessInitiationAsyncResponse'
        '400':
          description: Invalid status request.
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '422':
          description: Background job has failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncInitiationRequestFailedResponse'

  /au/payto/pam-v1/PaymentAgreementAsync:
    post:
      tags:
        - PayTo Async API
      summary: Create a Payment Agreement Async
      description: |
        The purpose of this endpoint is to asynchronously create a payment agreement. The API returns as soon as the creation request has been captured and validated for background processing.

        The async API follows the Microsoft [async-request-reply pattern](https://learn.microsoft.com/en-us/azure/architecture/patterns/async-request-reply).

        Following a successful call, the status of the background request can be monitored using [Get Async Request Status (Payment Instruction)](#tag/PayTo-Async-API/operation/get-async-paymentagreement-status) or by subscription to the [AsyncJobResultNotification](#tag/Webhooks/operation/AsyncRequestNotification) webhook.

        The background request will complete when the payment agreement is either:
        - Created (Note: This does not necessarily mean the payment agreement has been activated.)
        - Failing with a non-recoverable error (retries will occur for transient errors such as timeouts)
        - Timed out on retries (set to 4 days).

        After request completion, the status of the payment agreement can be determined by calling [Get Payment Agreement by UniqueId](#tag/Reporting/operation/get-paymentagreement-id) or by subscription to the [PaymentAgreementNotification](#tag/Webhooks/operation/PaymentAgreementNotification) webhook.
        The request payload and business rules/validations are identical to the synchronous API.
      operationId: post-paymentagreement-async
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentAgreementDto'
      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  /au/payto/pas-v1/PaymentInstructionAsync:
    post:
      tags:
        - PayTo Async API
      summary: Create Payment Instruction Async
      description: |
        The purpose of this endpoint is to asynchronously create a payment instruction. The API returns as soon as the creation request has been captured and validated for background processing.

        The async API follows the Microsoft [async-request-reply pattern](https://learn.microsoft.com/en-us/azure/architecture/patterns/async-request-reply).

        Following a successful call, the status of the background request can be monitored using the [Get Async Request Status (Payment Instruction)](#tag/PayTo-Async-API/operation/get-async-paymentinitiation-status) endpoint or by subscription to the [AsyncJobResultNotification](#tag/Webhooks/operation/AsyncRequestNotification) webhook.

        The background request will complete when the payment instruction is either:
        - Created (This does not necessarily mean the payment has already been finalized).
        - Failing with a non-recoverable error. The background request will do retries for transient errors such as timeouts.
        - Timed out on retries (set to 4 days).

        After request completion, the status of the payment can be determined by calling [Get Payment Instruction By UniqueId](#tag/Reporting/operation/get-paymentinstruction-paymentinitiationid) or by subscription to the [Get Payment Instruction By UniqueId](#tag/Webhooks/operation/PaymentInstructionStatusNotification) webhook.

        The request payload and business rules/validations are identical to the synchronous API.
      operationId: post-async-paymentinstruction
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateMandatePaymentDto'
            example:
              paymentAgreementUID: BCORP123456
              paymentInitiationUID: BCORP00000001
              paymentDetails:
                amount: 1.00
                lodgementReference: 'BCorp PIR 001'

      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/PaymentAgreementAsync/{paymentAgreementUID}/unilateral':
    patch:
      tags:
        - PayTo Async API
      summary: Unilateral Amendment Async
      description:
        The purpose of this endpoint is to asynchronously amend a payment agreement that does not require approval from the Payer.  The API returns as soon as the amend request has been captured and validated for background processing.

        The request payload and business rules/validations are identical to the synchronous API.
      operationId: patch-async-paymentagreement-id-unilateral
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            description: Id to uniquely Identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            example: MONPAG12345
            nullable: true
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/AmendUnilateralDetailsDto'
            example:
              description: payroll pag1
              shortDescription: payroll pag1
              payeeDetails:
                payeeType: ORGN
                payeeLinkedBsb: 802980
                payeeLinkedAccount: ********
                payeeLinkedPayId: <EMAIL>
                payeeLinkedPayIdType: EMAIL
                payeeAccountName: PayCo
                ultimatePayee: PayCo
      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'b008558d-b6df-4935-b2aa-39b2f8d467be',
                  'errors':
                    [
                      {
                        'errorCode': 'PAM_BR_AMEND_FORBIDDEN',
                        'errorMessage': 'Can only amend payment agreement in active or paused status'
                      }
                    ]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/PaymentAgreementAsync/{paymentAgreementUID}/bilateral':
    patch:
      tags:
        - PayTo Async API
      summary: Bilateral Amendment Async
      operationId: patch-async-paymentagreement-id-bilateral
      description:
        The purpose of this endpoint is to asynchronously amend a payment agreement which requires approval from the Payer. Changes made using this end point will only get reflected after payer approves the change.  The API returns as soon as the amend request has been captured and validated for background processing.

        The request payload and business rules/validations are identical to the synchronous API.
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            description: Id to uniquely Identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            example: MONPAG12345
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AmendBilateralDetailsDto'

      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/PaymentAgreementAsync/{paymentAgreementUID}/status':
    patch:
      tags:
        - PayTo Async API
      summary: Amend Payment Agreement Status Async
      description:
        This endpoint allows the initiator to change the status of a payment agreement. Available statuses are active, pause or cancel.  The API returns as soon as the amend request has been captured and validated for background processing.

        The request payload and business rules/validations are identical to the synchronous API.
      operationId: patch-async-paymentagreement-id-status
      parameters:
        - name: paymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            description: Id to uniquely Identify a payment agreement.
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            example: MONPAG12345
            nullable: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AmendPaymentAgreementStatusDto'
            example:
              statusChange: pause
              reasonCode: AC02
              reasonDescription: Amend mandate status

      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/payto/pam-v1/PaymentAgreementAsync/{PaymentAgreementUID}/recall/{actionIdentification}':
    patch:
      tags:
        - PayTo Async API
      summary: Recall a Payment Agreement Async
      description:
        The purpose of this endpoint is to asynchronously recall a pending PAG creation/amendment action before it is approved or declined by payer servicer/customer. We will cancel the action uniquely identified by actionIdentification.  The API returns as soon as the recall request has been captured and validated for background processing.

        The request payload and business rules/validations are identical to the synchronous API.
      operationId: patch-async-paymentagreement-id-recall
      parameters:
        - name: PaymentAgreementUID
          in: path
          required: true
          schema:
            type: string
            maxLength: 35
            pattern: ^[A-Za-z0-9_-]
            description: Id to uniquely Identify a payment agreement.
            example: MONPAG12345
        - name: actionIdentification
          in: path
          required: true
          schema:
            type: string
            maxLength: 35
            pattern: ^[a-f0-9]{12}1[a-f0-9]{3}[89ab][a-f0-9]{15}$
            description: unique Id used to identify pending(unapproved) actions. This id is assigned when a new payment agreement is created or when a bilateral amendment is made.
            example: 6ae818a80ba362618aeaece46ee70e4a
      responses:
        '202':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncGenericResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  # webhooks
  /WEBHOOK_TARGET_URL:
    post:
      tags:
        - Webhooks
      summary: Payment Agreement Notification
      description: "
        <p> When a payer makes a change to a payment agreement (for example authorising or cancelling a payment agreement through their banking app) payment agreement, Monoova will send a webhook to notify the initiator. Payment Agreement webhooks will provide the data included in the create payment agreement schema
        <br/>
        Event name: PaymentAgreementNotification
        </p></br>
        These webhook can be subscribed using API's provided in <a href='/payTo#tag/Notification-Management'>Notification Management</a>.
        "
      operationId: PaymentAgreementNotification
      security: []
      responses:
        '200':
          description: OK - Notification successfully received.
      parameters:
        - name: Authorization
          in: header
          required: false
          schema:
            type: string
          example: 'Bearer <TOKEN>'
          description: The securityToken from the subscription.
        - name: Verification-Signature
          in: header
          required: false
          schema:
            type: string
          example: 'e+AFAj2W69rAwbsGn+rSSnFm2ISEblo0MXnx9Qtoh2k5mst1cEEpcrVSzGL
            jzOPlEL2Ea/iYLbFGzDdxVRTcNLINOhsXM/smimNjBt8sq30FbvSNMjlfDn
            rZ6FOIkl3E3cu9B+M4OVL8HafPohb67IRNDNyCnCvBM10qHrioiak='
          description: This is a base64 encoded cryptographic signature that should be used<br/>
            to verify both the integrity of the message as well as the source (Monoova). <br/>
            The signature's hashing method is SHA256 and the public key can be retrieved from
            <a href="/payments#operation/PublicCertificatePublicKey"> /public/v1/certificate/public-key </a>.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookPaymentAgreementDto'
            example:
              eventId: 9f0f390c-6173-4977-abd0-9f41218a62a3
              sourceEventName: PaymentAgreementCancelled
              eventTimestamp: 2022-08-31T07:12:35.7560202Z
              paymentAgreementDetails:
                paymentAgreementUID: BCORP123456
                mmsId: 'e57577f82e841bf3b0edbacfdc775ca0'
                paymentAgreementStatus: 'Cancelled'
                payeeDetails:
                  payeeType: ORGN
                  payeeLinkedBsb: 802950
                  payeeLinkedAccount: ********
                  payeeLinkedPayID: <EMAIL>
                  payeeLinkedPayIdType: EMAIL
                  payeeAccountName: PayCo
                  ultimatePayee: PayCo
                payerDetails:
                  payerType: ORGN
                  linkedBsb: 802950
                  linkedAccount: *********
                  payer: WidgetCo
                  ultimatePayer: WidgetCo
                  payerPartyReference: Payer4321
                paymentTerms:
                  numberOfTransactionsPermitted: 100
                  frequency: WEEK
                  maximumAmount: 100.00
                  agreementType: VARI
                paymentDetails:
                  automaticRenewal: false
                  description: payroll pag
                  shortDescription: PayToTest123445
                  purpose: MORI
                  respondByTime: 03/22/2024 15:45:30
                  startDate: 2022-10-05
                  endDate: 2023-08-24
              action:
                agreementStatusChangeReasonCode: M002
                agreementStatusChangeReasonDescription: Reason description, manually entered, or mapped from agreementStatusChangeReasonCode.
                actionIdentification: dccbd8bb810618e1891e9fde2379345e
                actionType: Amend
                actionStatus: Expired

  /Webhook_Payment_Instruction_Status_URL:
    post:
      tags:
        - Webhooks
      summary: Payment Instruction Status Notification
      parameters:
        - name: Authorization
          in: header
          required: false
          schema:
            type: string
          example: 'Bearer <TOKEN>'
          description: The securityToken from the subscription.
        - name: Verification-Signature
          in: header
          required: false
          schema:
            type: string
          example: 'e+AFAj2W69rAwbsGn+rSSnFm2ISEblo0MXnx9Qtoh2k5mst1cEEpcrVSzGL
            jzOPlEL2Ea/iYLbFGzDdxVRTcNLINOhsXM/smimNjBt8sq30FbvSNMjlfDn
            rZ6FOIkl3E3cu9B+M4OVL8HafPohb67IRNDNyCnCvBM10qHrioiak='
          description: This is a base64 encoded cryptographic signature that should be used<br/>
            to verify both the integrity of the message as well as the source (Monoova). <br/>
            The signature's hashing method is SHA256 and the public key can be retrieved from
            <a href="/payments#operation/PublicCertificatePublicKey"> /public/v1/certificate/public-key </a>.
      description: "
        <p> Clients need to implement an API endpoint on their system to receive PaymentInstructionStatus notifications from Monoova.
        <br/>
        Event name: PaymentInstructionNotification
        </p></br>
        These webhook can be subscribed using API's provided in <a href='/payTo#tag/Notification-Management'>Notification Management</a>.
        "
      operationId: PaymentInstructionStatusNotification
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookPaymentInstructionDto'
            example:
              eventId: 7C1C78DB-BD64-416A-96C4-661C89602FDC
              sourceEventName: PaymentInstructionStatusUpdated
              eventTimestamp: 2023-06-05T04:43:05.9904048Z
              paymentInitiationDetail:
                paymentAgreementUID: MONPAG1683781294
                paymentInitiationUID: PIU-**********
                nppInstructionId: PI123456789
                paymentInitiationStatus: ACSC
                paymentInitiationStatusDescription: Accepted & Settled
                mmsId: 4fdbeebadff71da0b5ccd9d25427c3df
              payeeDetails:
                payeeType: Organisation
                payeeLinkedBSB: ''
                payeeLinkedAccount: ''
                payeeLinkedPayID: <EMAIL>
                payeeLinkedPayIdType: Email
                payeeAccountName: ABC Corp1
                ultimatePayee: ABC Corp1
              paymentDetails:
                amount: 10.00
                isLastPayment: false
                lodgementReference: Monoova PayTo LVP Demo Testing
      responses:
        '200':
          description: OK - Notification successfully received.

  '/WEBHOOK_ASYNC_REQUEST_URL':
    post:
      tags:
        - Webhooks
      parameters:
        - name: Authorization
          in: header
          required: false
          schema:
            type: string
          example: 'Bearer <TOKEN>'
          description: The securityToken from the subscription.
        - name: Verification-Signature
          in: header
          required: false
          schema:
            type: string
          example: 'e+AFAj2W69rAwbsGn+rSSnFm2ISEblo0MXnx9Qtoh2k5mst1cEEpcrVSzGL
            jzOPlEL2Ea/iYLbFGzDdxVRTcNLINOhsXM/smimNjBt8sq30FbvSNMjlfDn
            rZ6FOIkl3E3cu9B+M4OVL8HafPohb67IRNDNyCnCvBM10qHrioiak='
          description: This is a base64 encoded cryptographic signature that should be used<br/>
            to verify both the integrity of the message as well as the source (Monoova). <br/>
            The signature's hashing method is SHA256 and the public key can be retrieved from
            <a href="/payments#operation/PublicCertificatePublicKey"> /public/v1/certificate/public-key </a>.
      summary: Asynchronous Request Notification
      description:
        This webhook can be subscribed using API's provided in <a href='/payTo#tag/Notification-Management'>Notification Management</a>.
        </br>
        Event name - <b>AsyncJobResultNotification</b>
      operationId: AsyncRequestNotification
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookAsyncRequestResponseDto'
            example:
              {
                eventId: '9E151397-BCA4-4700-8718-3907368780F5',
                sourceEventName: 'AsyncJobCompleted',
                eventTimestamp: '2023-04-18T05:34:22.3447159Z',
                uniqueRequestId: 'b78e1829-dd2e-4119-a678-af8abccf9129',
                status: 'Failed',
                entityType: 'PaymentAgreement',
                actionType: 'Create',
                location: 'https://api.monoova.com/au/payto/pam/MONPAG2323232',
                errorCode: 'PAM_UNEXPECTED_ERROR',
                errorMessage: 'Internal server error. Please contact Monoova support.'
              }
      responses:
        '200':
          description: Any Response
  # Notification Management
  /au/core/notification-v1/Subscription:
    get:
      tags:
        - Notification Management
      summary: Get All Subscriptions
      description: Retrieves details of all subscriptions.
      operationId: getAllSubscriptions
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionListResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
    post:
      tags:
        - Notification Management
      summary: Create a subscription
      operationId: post-subscription
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionDetailUpdateRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionUpdateResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  /au/core/notification-v1/Subscription/{subscriptionId}:
    get:
      tags:
        - Notification Management
      summary: Get Subscription by Id
      description: Gets the Subscription corresponding to the subscriptionId.
      operationId: get-subscription
      parameters:
        - name: subscriptionId
          in: path
          required: true
          schema:
            type: string
            description: Monoova generated Guid to uniquely identify the subscription. </br> This is required when updating a subscription, if not provided then we assume we are trying to create a new subscription.</br>Is a valid Guid when not blank.

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionListResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
    delete:
      tags:
        - Notification Management
      summary: 'Delete a subscription'
      operationId: delete-subscription-id
      parameters:
        - name: subscriptionId
          in: path
          required: true
          schema:
            type: string
            description: Monoova generated Guid to uniquely identify the subscription. </br> This is required when updating a subscription, if not provided then we assume we are trying to create a new subscription.</br>Is a valid Guid when not blank.
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDeleteResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
    put:
      tags:
        - Notification Management
      summary: Update a subscription
      operationId: put-subscription
      parameters:
        - name: subscriptionId
          in: path
          required: true
          schema:
            type: string
            description: Monoova generated Guid to uniquely identify the subscription. </br> This is required when updating a subscription, if not provided then we assume we are trying to create a new subscription.</br>Is a valid Guid when not blank.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionDetailUpdateRequest'

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionUpdateResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  /au/core/notification-v1/Notification:
    get:
      tags:
        - Notification Management
      summary: Get Webhooks
      operationId: get-webhookschedule
      parameters:
        - name: start
          in: query
          description: Format - date-time (as date-time in RFC3339).
          schema:
            type: string
            format: date-time
        - name: pageSize
          in: query
          description: Format - int32.
          schema:
            type: integer
            format: int32
        - name: pageNumber
          in: query
          description: Format - int32.
          schema:
            type: integer
            format: int32
        - name: end
          in: query
          description: Format - date-time (as date-time in RFC3339).
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebHookListResponse'
              example:
                traceId: d5e0b5ee-2aff-4ac7-a638-893b664183a3
                notifications:
                  - eventId: 2a37987a-0486-4c6f-9267-9ff44eb168c6
                    sourceEventName: PaymentAgreement_Updated
                    url: https://f89be4be-781a-47cd-8048-8ba3f5438d56.mock.pstmn.io/PaymentAgreementNotificaion
                    eventTimestamp: 2022-08-29T03:11:01.471Z
                    executionCompleteDateTimeUtc: 2022-08-29T03:11:03.365Z
                    status: Success
                    statusDescription: Hello
                    lastResponseCode: 200
                    retryCount: 0
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  '/au/core/notification-v1/Notification/resend/{eventId}':
    post:
      tags:
        - Notification Management
      summary: 'Request a resend'
      operationId: post-webhookschedule-resend-eventid
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebHookResendResponse'
        '400':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '403':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '404':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '500':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '502':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }
        '504':
          description: Error
          content:
            application/json:
              example:
                {
                  'traceId': 'f1a7fbd7-ca39-4b98-b247-f4e0e004f152',
                  'errors': [{ 'errorCode': 'string', 'errorMessage': 'string' }]
                }

  # Authentication - PayTo
  /au/security/oauth-v1/Token:
    post:
      summary: Token
      tags:
        - Generate a Bearer Token
      description: </br><p>
        This endpoint will generate a bearer token which is required to call the Monoova PayTo endpoints. Bearer tokens are valid for 24 hours so they need to be regenerated every 24 hours. Once a bearer token has been generated payment agreements can be created, and payments initiated against those agreements.
        </p></br>
        This API endpoint uses BASIC Authentication.
        </br> <style type="text/css">
        .tb { table-layout:auto; width:200px;!important }
        .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
        </style>
        <table class="tb">

        <tr>
        <td class="td">
        username <br/>
        </td>
        <td class="td">
        Your mAccount number that can be obtained from portal <br/>
        </td>
        </tr>
        <tr>
        <td class="td">

        password <br/>
        </td>
        <td class="td">
        Your API key that can be obtained from portal <br/>
        </td>
        </tr>
        </table>
        </br>
        <p>
        After 10 failed authentication attempts your account will be blocked. You will receive a 401 Forbidden error when you fail authentication. Once your account is blocked you will receive a 401 Too Many Requests error. To unlock your account you need to roll your API key in the Monoova portal.
        </p></br>
        <b>Please note that rolling your API key will impact you existing API integration, you will need to update your API across your environment.</b>

      operationId: oauth-v1/token
      security:
        - BasicAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                example:
                  {
                    'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
                  }

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    BasicAuth:
      type: http
      scheme: basic
  schemas:
    #PayTo
    AmendBilateralDetailsDto:
      type: object
      properties:
        automaticRenewal:
          type: boolean
          nullable: true
        endDate:
          type: string
          description: End date of the validity of the mandate. If specified, the mandate is valid until 23:59:59.999 Australia Sydney time on this date.
          nullable: true
        respondByTime:
          type: string
          format: date-time
          description:
            Optional time used to indicate by when resolution of a bilateral action is requested from the other party to the payment agreement. It will be provided in any notification sent to the counterparty. This time is for informational purposes only and does not affect the expiry time imposed by the MMS.
            Must be future date and less than 5 days
            If not provided value will be defaulted to agreement creation datetime + 5 day
            Default time zone is UTC
          nullable: true
        paymentTerms:
          $ref: '#/components/schemas/PaymentTermsDto'
      additionalProperties: false
    AmendBilateralDetailsResponse:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: MONPAG12345
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: 684211c8a4076eeea68e63eeaa8ea6aa
        actionIdentification:
          type: string
          example: eea687ee114eae368ca066aa62bae488
      additionalProperties: false
    AmendPaymentAgreementStatusDto:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: |
            Unique ID of the payment agreement.
        statusChange:
          type: string
          description: The status that you wish to change the payment agreement to.
            </br> <strong> Note&#58;</strong> You cannot change a status from ‘Created’ to ‘Cancel’. If you wish to recall a payment agreement before the payer has approved it, you need to use the Recall Agreement endpoint.</b>
            </br> <style type="text/css">
            .tb { table-layout:auto; width:300px;!important }
            .td { overflow:hidden; white-space:nowrap; text-overflow:ellipsis!important}
            </style>
            <table class="tb">
            <tr>
            <th> Current Status </th>
            <th> Payload Status </th>
            <th> New Status </th>
            <th> Note </th>
            </tr>
            <tr><td>	Active	</td><td>	Cancel	</td> <td>	Cancelled	</td><td>	Reason Code is mandatory.	</td></tr>
            <tr><td>	Active 	</td><td>	Pause	</td><td>	Paused	</td><td>	Reason Code is mandatory.	</td></tr>
            <tr><td>	Paused	</td><td>	Resume	</td><td>	Active	</td><td>	Only party who suspended can Activate payment agreement	</td></tr>
            <tr><td>	Paused	</td><td>	Cancel	</td><td>	Cancelled	</td><td>	Reason Code is mandatory.	</td></tr>
            <tr><td>	Cancel	</td><td>-</td><td>-</td><td>	This is the final status	</td></tr>
            </table>
        reasonCode:
          type: string
          description: |
            Reason code for changing the agreement status. Must be provided when the status is `Cancel` or `Pause`.
          example: AC02
        reasonDescription:
          type: string
          description: |
            Description of the reason for the change in status.
          example: Invalid Debtor Account Number
          maxLength: 256
    AmendPaymentAgreementStatusResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Trace ID for tracking the request.
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: MONPAG12345
        paymentAgreementStatus:
          type: string
          description: |
            The current status of the payment agreement.
          example: Paused
        paymentAgreementStatusDescription:
          type: string
          description: |
            Detailed description of the current status.
          example: Status changed from Active to Paused
        mmsId:
          type: string
          description: |
            Unique ID that identifies this payment agreement within the system.
    AmendUnilateralDetailsDto:
      type: object
      properties:
        description:
          type: string
          description: Reason for the mandate setup as narrative text.</br> Either "description" or "short_description" must be present.
          maxLength: 140
          example: payroll pag1
          nullable: true
        shortDescription:
          type: string
          description: Reason for the mandate setup as narrative text.</br> Either "description" or "short_description" must be present.
          maxLength: 35
          example: payroll pag1
          nullable: true
        payeeDetails:
          $ref: '#/components/schemas/PayeeDetailsDto'
      additionalProperties: false
    AmendUnilateralDetailsResponse:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: BCORP1662358223
        traceId:
          type: string
          example: 1dabfaa9-abe8-4d2d-a69a-b723079d84ed
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: aa0a5d7eedcd1c01999b2906b1edd8cd
        actionIdentification:
          type: string
          example: b9c91b0f382b1fa5bf7e87aaad094eee
      additionalProperties: false
    CreatePaymentAgreementDto:
      type: object
      required:
        - paymentAgreementUID
      properties:
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: '^[A-Z0-9_-]'
          example: ABCORP123456
        payeeDetails:
          $ref: '#/components/schemas/PayeeDetailsDto'
        payerDetails:
          $ref: '#/components/schemas/PayerDetailsDto'
        paymentTerms:
          $ref: '#/components/schemas/PaymentTermsDto'
        paymentDetails:
          $ref: '#/components/schemas/PaymentDetailsDto'
      additionalProperties: false
    WebhookPaymentAgreementDto:
      type: object
      required:
        - paymentAgreementUID
      properties:
        eventId:
          type: string
          minLength: 40
          pattern: '^[A-Z0-9-]'
          description: This will uniquely identify each notification and can be used to request a resend.
        sourceEventName:
          type: string
          description: Possible Values PaymentAgreementCreated, PaymentAgreementActive, PaymentAgreementCancelled, PaymentAgreementAmended, PaymentAgreementStatusAmended, PaymentAgreementActionCancelled, PaymentAgreementActionExpired (since 1.03), PaymentAgreementActionDeclined (since 1.03), PaymentAgreementActionCreated (since 1.06)
        eventTimestamp:
          type: string
          format: date-time
          description: ISO Date Time in UTC
        paymentAgreementDetails:
          type: object
          properties:
            paymentAgreementUID:
              type: string
              description: Id to uniquely Identify a payment agreement.
              maxLength: 35
              pattern: '^[A-Z0-9_-]'
            paymentAgreementStatus:
              type: string
              description: Status of the payment agreement. Must be one of Created, Active, Paused, Cancelled, Rejected, Expired
            payeeDetails:
              $ref: '#/components/schemas/PayeeDetailsDto'
            payerDetails:
              $ref: '#/components/schemas/PayerDetailsDto'
            paymentTerms:
              $ref: '#/components/schemas/PaymentTermsDto'
            paymentDetails:
              $ref: '#/components/schemas/PaymentDetailsDto'
        action:
          type: object
          description: This will be populated when notifications are triggered for following events
            Mandate cancellation, declined by Payer, Mandate status changed by Payer, Mandate amendment declined by Payer.
          properties:
            agreementStatusChangeReasonCode:
              type: string
              description: <a href='/payTo#tag/Mandate-Status-Reason-Codes'>See Mandate Status Reason Code</a>
            agreementStatusChangeReasonDescription:
              type: string
              description: Description is one of the manually inserted reason description when responding to the action, The relevant description attached to the code above from list <a href='/payTo#tag/Mandate-Status-Reason-Codes'>Mandate Status Reason Code</a>.
            actionStatus:
              type: string
              description: One of <br/>Completed,<br/>Cancelled,<br/>PendingApproval,<br/>Declined,<br/>Expired,<br/>Rejected
            actionType:
              type: string
              description: One of <br/>CREATE,<br/>AMEND,<br/>PAUSE,<br/>RESUME,<br/>CANCEL,<br/>PORT
            actionIdentification:
              type: string
      additionalProperties: false
    WebhookPaymentInstructionDto:
      type: object
      required:
        - paymentInitiationUID
      properties:
        eventId:
          type: string
          minLength: 40
          pattern: '^[A-Z0-9-]'
          description: This will uniquely identify each notification and can be used to request a resend.
        sourceEventName:
          type: string
          description: PaymentInstructionStatusUpdated
        eventTimestamp:
          type: string
          format: date-time
          description: ISO Date Time in UTC
        paymentInitiationDetails:
          type: object
          required:
            - paymentAgreementUID
          properties:
            paymentAgreementUID:
              type: string
              description: Id to uniquely Identify a payment agreement.
              maxLength: 35
              pattern: '^[A-Z0-9_-]'
            paymentInitiationUID:
              type: string
              nullable: true
              maxLength: 35
              description: Id to uniquely identify payment
            paymentInitiationStatus:
              type: string
              nullable: true
              description: This is the status of the payment agreement.
              example: ACSC
            paymentInitiationStatusDescription:
              type: string
              description: This describes the status. For example, a status of ‘created’ will have a description of ‘Pending Payer Approval’
              example: Accepted & Settled
              nullable: true
            mmsId:
              type: string
              description: This is the unique ID that identifies this payment agreement.
              example: 4532543543543
              nullable: true
            statusReasonCode:
              type: string
              example: null
            statusReasonDescription:
              type: string
              example: null
        payeeDetails:
          $ref: '#/components/schemas/PayeeDetailsDto'
        paymentDetails:
          $ref: '#/components/schemas/PaymentInitiationDetailsDto'
      additionalProperties: false
    CreatePaymentAgreementResponse:
      type: object
      properties:
        traceId:
          type: string
          example: 1dabfaa9-abe8-4d2d-a69a-b723079d84ed
        paymentAgreementUID:
          type: string
          maxLength: 35
          pattern: ^[A-Za-z0-9_-]
          description: Id to uniquely Identify a payment agreement.
          example: BCORP123456
        PaymentAgreementStatus:
          type: string
          description: This is the status of the payment agreement. Status (e.g. Active, Cancelled, ...). See <a href="/payTo#tag/Payment-Agreement-Status">Payment Agreement Status</a> for all options.
          example: Created
        PaymentAgreementStatusDescription:
          type: string
          description: This describes the status. For example, a status of ‘created’ will have a description of ‘Pending Payer Approval’
          example: Pending Payer Approval
          nullable: true
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: a9ba56f64baa1dee9ac2d7f87c2621fa
        registrationDateTime:
          type: string
          description: The date and time that you created the mandate.
          example: 2022-09-05T05:29:53.031Z
        actionIdentification:
          type: string
          example: b9c91b0f382b1fa5bf7e87aaad094eee
      additionalProperties: false
    #Async Schemas
    AsyncGenericResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Unique identifier for this request instance, useful for tracing.
          example: 'ac819ad0-c3ee-44f9-964a-34a0a5ff5a24'
        uniqueRequestId:
          type: string
          description: Unique identifier assigned to the request to correlate with the response.
          example: '595a3202-e9aa-406e-a2a3-5efb6ca8b72a'
    PendingInitiationAsyncResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Error log trace identifier.
          example: 'be393fae-52c2-4c9e-a262-4974de6da750'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '4834dba5-27b0-465c-aea7-aa530315c68c'
        paymentInitiationUID:
          type: string
          description: User supplied PIR identifier.
          example: 'MONPIR1673935776'
        status:
          type: string
          description: Current status of the request.
          example: 'Processing'
    SuccessInitiationAsyncResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Error log trace identifier.
          example: 'c0905f60-80b7-4a6a-abe3-0207b3e19b8e'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '595a3202-e9aa-406e-a2a3-5efb6ca8b72a'
        paymentInitiationUID:
          type: string
          description: User supplied PIR identifier.
          example: 'MONPIR1673930138'
        status:
          type: string
          description: Final status of the request.
          example: 'Completed'
    AsyncInitiationRequestFailedResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Error log trace identifier.
          example: 'c0905f60-80b7-4a6a-abe3-0207b3e19b8e'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '595a3202-e9aa-406e-a2a3-5efb6ca8b72a'
        paymentInitiationUID:
          type: string
          description: User supplied PIR identifier.
          example: 'MONPIR1673930138'
        status:
          type: string
          description: Final status of the request indicating failure.
          example: 'Failed'
        errorCode:
          type: string
          description: Error code indicating the reason for failure.
          example: 'MOV_UNEXPECTED_VALIDATION_FAILURE'
        errorMessage:
          type: string
          description: Detailed message explaining the reason for failure.
          example: 'Validation failed with banking partner. Contact Monoova for more information'
    PendingAgreementAsyncResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Trace identifier.
          example: 'be393fae-52c2-4c9e-a262-4974de6da750'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '4834dba5-27b0-465c-aea7-aa530315c68c'
        paymentAgreementUID:
          type: string
          description: User supplied PAG identifier.
          example: 'MONPAG1673935776'
        status:
          type: string
          description: Current status of the request.
          example: 'Processing'
    SuccessAgreementAsyncResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Error log trace identifier.
          example: 'c0905f60-80b7-4a6a-abe3-0207b3e19b8e'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '595a3202-e9aa-406e-a2a3-5efb6ca8b72a'
        paymentAgreementUID:
          type: string
          description: User supplied PAG identifier.
          example: 'MONPAG1673930138'
        status:
          type: string
          description: Final status of the request.
          example: 'Completed'
    AsyncMandateRequestFailedResponse:
      type: object
      properties:
        traceId:
          type: string
          description: Error log trace identifier.
          example: 'c0905f60-80b7-4a6a-abe3-0207b3e19b8e'
        uniqueRequestId:
          type: string
          description: Server generated unique request identifier.
          example: '595a3202-e9aa-406e-a2a3-5efb6ca8b72a'
        paymentAgreementUID:
          type: string
          description: User supplied PAG identifier.
          example: 'MONPAG1673930138'
        status:
          type: string
          description: Final status of the request indicating failure.
          example: 'Failed'
        errorCode:
          type: string
          description: Error code indicating the reason for failure.
          example: 'MOV_UNEXPECTED_VALIDATION_FAILURE'
        errorMessage:
          type: string
          description: Detailed message explaining the reason for failure.
          example: 'Validation failed with banking partner. Contact Monoova for more information'
    WebhookAsyncRequestResponseDto:
      type: object
      required:
        - uniqueRequestId
      properties:
        eventId:
          type: string
          description: eventId must be a string not an integer. It should allow at least 40 characters including alphanumerics and hyphen “-”
        sourceEventName:
          type: string
          enum:
            - CreditCardRefundUpdated
        eventTimestamp:
          type: string
          format: date-time
          description: ISO Date Time in UTC
        uniqueRequestId:
          type: string
          pattern: '[a-z0-9-]{36}'
          description: The async request Id returned by the original async API call
        status:
          type: string
          description: One of <br/> Completed - successful completion <br/> Failed - error encountered <br/> Expired - request could not be completed within a reasonable time (currently 48 hours)
        location:
          type: string
          format: uri
          description: URL Location of the updated resource <br/> Only if status = Completed
        errorCode:
          type: string
          description: Only if status = Failed
        errorMessage:
          type: string
          description: Only if status = Failed
        entityType:
          type: string
          enum:
            - PaymentAgreement
            - PaymentInstruction
            - CreditCardPayment
            - CreditCardRefund
          description: The type of the entity involved in the transaction.

        actionType:
          type: string
          enum:
            - Create
            - Amend
            - Cancel
            - RecallAction
            - AmendStatus
          description: >
            The type of action being performed. 
            - 'Create' is valid for PaymentAgreement, PaymentInstruction, CreditCardRefund.
            - 'Amend' is valid for PaymentAgreement.
            - 'Cancel' is valid for CreditCardPayment (Note: Cancellations are currently disabled).
            - 'RecallAction' and 'AmendStatus' are valid for PaymentAgreement.
      additionalProperties: false
    GetPaymentInstructionStatusByDateResponse:
      type: object
      properties:
        pageNumber:
          type: integer
          description: The number of the current page, starting at 1 (1-indexed).
        pageSize:
          type: integer
          description: The number of records per page.
        recordCount:
          type: integer
          description: The total number of records.
        paymentInitiationDetails:
          type: array
          items:
            $ref: '#/components/schemas/PaymentStatusResponseDto'
      additionalProperties: false
    InitiateMandatePaymentDto:
      type: object
      required:
        - paymentAgreementUID
        - paymentInitiationUID
        - paymentDetails
      properties:
        paymentAgreementUID:
          type: string
          maxLength: 35
          description: Id to uniquely Identify a payment agreement.
          nullable: true
        paymentInitiationUID:
          type: string
          nullable: true
          maxLength: 35
          description: Id to uniquely identify payment
        payeeDetails:
          $ref: '#/components/schemas/PayeeDetailsDto'
        paymentDetails:
          $ref: '#/components/schemas/PaymentInitiationDetailsDto'
      additionalProperties: false
    InitiateMandatePaymentResponseDto:
      type: object
      properties:
        traceId:
          type: string
          example: f3ae20c5-e400-4eab-8fb0-16e03c8481be
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: MOA1222333
        paymentInitiationUID:
          type: string
          description: Id to uniquely Identify a payment initiation.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: C150D6FAE2044CA9A3ADB470874E8F86
        nppInstructionId:
          type: string
          description: Unique identifier for the NPP instruction.
          maxLength: 35
          example: MOPYAUS1XXXI20241119004756058205270
        paymentInitiationStatus:
          type: string
          description: This is the status of the payment agreement.
          example: ACSC
        paymentInitiationStatusDescription:
          type: string
          description: This describes the status. For example, a status of ‘created’ will have a description of ‘Pending Payer Approval’
          example: Accepted & Settled
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: ed8569b5fd8616259d15b5b9a2d52bc0
      additionalProperties: false
    PayeeDetailsDto:
      required:
        - payeeType
      oneOf:
        - required: [payeeLinkedBsb, payeeLinkedAccount]
        - required: [payeeLinkedPayId, payeeLinkedPayIdType, payeeAccountName]
      type: object
      properties:
        payeeType:
          type: string
          nullable: true
          description: Identifies the type of a party.</br> Possible Values ORGN - Organization, PERS - Person
          example: ORGN
        payeeLinkedBsb:
          type: string
          description: BSB number. </br>Required if 'payeeLinkedPayId' and 'payeeLinkedPayIdType' are not provided. e.g. '999-999 or 999999'
          example: 999-999
          nullable: true
        payeeLinkedAccount:
          type: string
          maxLength: 28
          description: Bank Account Number. </br>Required if 'payeeLinkedPayId' and 'payeeLinkedPayIdType' are not provided.
          nullable: true
          example: ********
        payeeLinkedPayId:
          type: string
          description: PayID. </br> Email, phone number, ABN, ACN or Organisation name. </br>Required if 'payeeLinkedBsb' and 'payeeLinkedAccount' are not provided. <a href="/payTo#tag/PAYID-Format-Validation">See PAYID Format Validation.</a>
          nullable: true
          example: ''
        payeeLinkedPayIdType:
          type: string
          description: PayID Type. Possible values ABN, Email, PhoneNumber, OrganisationId, ACN. </br>Required if 'payeeLinkedBsb' and 'payeeLinkedAccount' are not provided.
          nullable: true
          example: ''
        payeeAccountName:
          type: string
          description: Bank Account Name </br> Can only provide payeeAccountName in the payload if crediting an external BSB. Mandatory if crediting a PayID.
          maxLength: 140
          nullable: true
          example: ABCORP
        ultimatePayee:
          type: string
          nullable: true
          example: ABCORP
          description: Name by which the party is known, and which is usually used to identify that party.</br> Can only provide ultimatePayee in the payload if crediting an external BSB.
      additionalProperties: false
    PayeeDetailsResponseDto:
      type: object
      properties:
        payeeType:
          type: string
          description: 'Specifies whether the payee is an organization or an individual.'
          example: null
        payeeLinkedBsb:
          type: string
          description: 'Bank State Branch (BSB) number identifying the bank branch of the payee.'
          example: 802950
          nullable: true
        payeeLinkedAccount:
          type: string
          description: 'Account number of the payee.'
          example: ********
          nullable: true
        payeeLinkedPayId:
          type: string
          description: "PayID associated with the payee's account."
          example: null
          nullable: true
        payeeLinkedPayIdType:
          type: string
          description: "Type of PayID linked to the payee's account."
          example: null
          nullable: true
        payeeAccountName:
          type: string
          description: "Name associated with the payee's bank account."
          example: BCORP
          nullable: true
        ultimatePayee:
          type: string
          description: 'The final recipient of the funds.'
          example: BCORP
      additionalProperties: false
    PayerDetailsDto:
      type: object
      required:
        - payerType
        - payer
        - ultimatePayer
        - payerPartyReference
      oneOf:
        - required: [linkedBsb, linkedAccount]
        - required: [linkedPayId, linkedPayIdType]
      properties:
        payerType:
          type: string
          description: Identifies the type of party. </br> Possible Values - ORGN (Organization),	PERS (Person)
          nullable: true
          example: ORGN
        linkedBsb:
          type: string
          description: BSB (Bank-State-Branch) number. </br>Required if 'linkedPayId' and 'linkedPayIdType' are not provided.
          nullable: true
          example: 802950
        linkedAccount:
          type: string
          description: Bank account number. </br>Required if 'linkedPayId' and 'linkedPayIdType' are not provided.
          maxLength: 28
          example: ********
          nullable: true
        linkedPayId:
          type: string
          nullable: true
          example: null
          description: PayID. </br> Email, phone number, ABN, ACN or Organisation ID. </br>Required if 'linkedBsb' and 'linkedAccount' are not provided. <a href="/payTo#tag/PAYID-Format-Validation">See PAYID Format Validation.</a>
        linkedPayIdType:
          type: string
          nullable: true
          example: null
          description: PayID Type. </br>Possible Values - Email, PhoneNumber, ABN, ACN, OrganisationId. </br>Required if 'linkedBsb' and 'linkedAccount' are not provided.
        payer:
          type: string
          description: Bank Account Name.
          maxLength: 140
          nullable: true
          example: ABCCORPCo
        ultimatePayer:
          type: string
          maxLength: 140
          description: Name by which the party is known, and which is usually used to identify that party.
          nullable: true
          example: ABCCORPCo
        payerPartyReference:
          type: string
          maxLength: 35
          description: This field will be presented to Payer in the detail agreement view. This field also be used group payment initiations associated with a payment agreement.
          nullable: true
          example: Payer54321
      additionalProperties: false
    PayerDetailsResponseDto:
      type: object
      properties:
        payerType:
          type: string
          description: "Type of the payer entity, such as 'ORGN' for organization."
          example: ORGN
        linkedBsb:
          type: string
          description: 'BSB number associated with the payer’s bank account.'
          example: 802950
          nullable: true
        linkedAccount:
          type: string
          description: 'Bank account number of the payer.'
          example: ********
          nullable: true
        linkedPayId:
          type: string
          description: 'PayID linked to the payer’s account, may be null if not used.'
          example: null
          nullable: true
        linkedPayIdType:
          type: string
          description: "Type of the linked PayID, such as 'Email' or 'Phone', may be null if PayID is not used."
          example: null
          nullable: true
        payer:
          type: string
          description: 'Name of the payer.'
          example: WidgetCo
        ultimatePayer:
          type: string
          description: 'The final entity responsible for making the payment.'
          example: WidgetCo
        payerPartyReference:
          type: string
          description: 'A reference identifier provided by the payer.'
          example: Payer1662333659
      additionalProperties: false
    PaymentAgreement:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: A user-provided unique identifier for the payment agreement.
          example: BCORP1662344139
          nullable: true
        paymentAgreementStatus:
          type: string
          description: Current status of the payment agreement. Status (e.g. Active, Cancelled, ...). See <a href="/payTo#tag/Payment-Agreement-Status">Payment Agreement Status</a> for all options.
          example: Paused
          nullable: true
        statusReasonCode:
          type: string
          example: R006
          nullable: true
        statusReasonDescription:
          type: string
          example: Account is now closed
          nullable: true
        mmsId:
          type: string
          description: A unique identifier for the payment agreement.
          example: d6765e4ff2eb1e7f83d72c1f8d3e2a07
          nullable: true
        payeeDetails:
          $ref: '#/components/schemas/PayeeDetailsResponseDto'
        payerDetails:
          $ref: '#/components/schemas/PayerDetailsResponseDto'
        paymentTerms:
          $ref: '#/components/schemas/PaymentTermsResponseDto'
        paymentDetails:
          $ref: '#/components/schemas/PaymentDetailsResponseDto'
        pendingActions:
          type: array
          items:
            $ref: '#/components/schemas/ActionDetails'
      additionalProperties: false

    ActionDetails:
      type: object
      properties:
        actionId:
          type: string
          description: A unique identifier for the action.
          example: '45df1d4abc914455a0e377051cb39fd7'
        actionType:
          type: string
          enum:
            - Create
            - Amend
          description: The type of action, which can be either 'Create' or 'Amend'.
        bilateral:
          type: boolean
          description: Indicates whether the action is bilateral.
          example: true
        status:
          type: string
          enum:
            - Completed
            - Cancelled
            - PendingApproval
            - Declined
            - Expired
            - Queued
          description: The current status of the action.
          example: PendingApproval
    PaymentAgreementResponse:
      type: object
      properties:
        traceId:
          type: string
          example: 43075658-ed9a-4ce0-84ba-2f6524a16676
        paymentAgreementDetails:
          $ref: '#/components/schemas/PaymentAgreement'
      additionalProperties: false
    PaymentAgreementsListResponse:
      type: object
      properties:
        paymentAgreementDetails:
          type: array
          items:
            $ref: '#/components/schemas/PaymentAgreement'
        traceId:
          type: string
          nullable: true
          example: 7fcb7dc4-ab8d-4bc8-8fa8-1335172377e2
      additionalProperties: false
    PaymentDetailsDto:
      required:
        - automaticRenewal
        - purpose
        - startDate
      properties:
        automaticRenewal:
          type: boolean
          nullable: true
          example: false
          description: Determines if the payment agreement automatically renews at the end of the defined period. </br> Possible values - True, False </br> endDate cannot be present if automaticRenewal is set as 'TRUE', endDate must be present if automaticRenewal is set as 'FALSE'
        description:
          type: string
          nullable: true
          description: Reason for the mandate setup as narrative text. </br> Either "description" or "short_description" must be present
          maxLength: 140
          example: null
        shortDescription:
          description: Reason for the mandate setup as narrative text. </br> Either "description" or "short_description" must be present
          type: string
          maxLength: 35
          nullable: true
          example: PayToTest_001
        purpose:
          type: string
          description: Payment Purpose Code.
            </br>  Possible Values-
            </br>  MORT - Mortgage Payments
            </br>  UTIL - Utility Payments
            </br>  LOAN - Loan Payments
            </br>  DEPD - Dependant Support Payments
            </br>  GAMP - Gambling Payments
            </br>  RETL - Retail Payments
            </br>  SALA - Salary Payments
            </br>  PERS - Personal Payment
            </br>  GOVT - Government Payments
            </br>  PENS - Pension Payments
            </br>  TAXS - Tax Payments
            </br>  OTHR - Other Service Payments
          nullable: true
          example: MORT
        respondByTime:
          type: string
          description: Optional time used to indicate by when resolution of a bilateral action is requested from the other party to the payment agreement. It will be provided in any notification sent to the counterparty. This time is for informational purposes only and does not affect the expiry time imposed by the MMS.<br/>Must be future date and less than 5 days<br/> If not provided value will be defaulted to agreement creation datetime + 5 day<br/> Default time zone is UTC
          format: ISODateTime ( yyyy-mm-ddThh:mm:ss+|–hh:mm)
          example: 2022-09-13T14:35:27Z
          nullable: true
        startDate:
          description: Start date of the validity of the mandate. The mandate is valid as of 00:00:00.000 Australia Sydney time on this date.
          type: string
          format: YYYY-MM-DD
          example: 2022-09-13
          nullable: true
        endDate:
          type: string
          format: YYYY-MM-DD
          example: 2023-08-24
          description: End date of the validity of the mandate. If specified, the mandate is valid until 23:59:59.999 Australia Sydney time on this date.
          nullable: true
      additionalProperties: false
    PaymentDetailsResponseDto:
      type: object
      properties:
        automaticRenewal:
          type: boolean
          description: Indicates if the agreement renews automatically
          example: false
        description:
          type: string
          description: Detailed reason for the mandate
          example: payroll pag
        shortDescription:
          type: string
          description: Brief reason for the mandate
          example: PayToTest_1662333659
        purpose:
          type: string
          description: Code indicating the payment's purpose.
          example: MORT
        respondByTime:
          type: string
          description: Time by which a response is needed.
          example: 2022-10-07T14:35:27-10
        startDate:
          type: string
          description: Start date of the mandate's validity.
          example: 2022-09-05
        endDate:
          type: string
          description: End date of the mandate's validity.
          example: 2023-08-24
      additionalProperties: false
    PaymentInitiationDetailsDto:
      type: object
      required:
        - amount
      properties:
        amount:
          type: string
          description: 'Amount to be transferred. Amount should be >=0.01'
          maxLength: 34
        isLastPayment:
          type: boolean
          nullable: true
          description: 'Set to true to indicate this is the last payment initiation request associated with the mandate. Possible values - True, False.'
        lodgementReference:
          type: string
          nullable: true
          description: 'Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.'
          maxLength: 280
      additionalProperties: false
    PaymentStatusResponseDto:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: MONPAG12345
        paymentInitiationUID:
          type: string
          description: Id to uniquely Identify a payment initiation.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: TRANSACTIONREFERRENCE2345
        paymentInitiationStatus:
          type: string
          description: A valid <a href='/payTo#tag/Payment-Initiation-Status'>Payment Initiation Status</a>.
          example: ACSC
        paymentInitiationStatusDescription:
          type: string
          description: This describes the  paymentinitiationstatus.
          example: Accepted & Settled
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: ed8569b4fd8616259d15bcb9a2da2bc0
        statusReasonCode:
          type: string
          example: R002
          nullable: true
        statusReasonDescription:
          type: string
          example: Account to be debited does not exist
          nullable: true
      additionalProperties: false
    PaymentTermsDto:
      required:
        - numberOfTransactionsPermitted
        - frequency
        - amount
        - agreementType
      type: object
      properties:
        numberOfTransactionsPermitted:
          type: integer
          format: int32
          description: Quantifies the number of transactions allowed per frequency
          nullable: true
          example: 10
        frequency:
          type: string
          description: Specifies the regularity of an event. See Frequency Table for allowed values
          nullable: true
          example: WEEK
        pointInTime:
          type: number
          description:
            Qualifies the frequency in terms of an exact point in time or moment within the specified period.</br> Valid non decimal numeric value. Supported values will be 01 to 31
            </br> This field works in combination with frequency and can only be provided when frequency = MNTH </br> This field should not be provided if numberOfTransactionsPermitted is provided
          nullable: true
          example: null
        amount:
          type: number
          nullable: true
          maxLength: 34
          example: null
          description: Exact amount that may be paid from the Creditor's account, per instruction. Refer to AgreementType & Amount table below
        maximumAmount:
          type: number
          maxLength: 34
          example: 100.00
          description: Maximum amount that may be paid from the Creditor's account, per instruction. Refer to AgreementType & Amount table below
          nullable: true
        agreementType:
          type: string
          description: Based on the AgreementType table
          nullable: true
          example: VARI
      additionalProperties: false
    PaymentTermsResponseDto:
      type: object
      properties:
        numberOfTransactionsPermitted:
          type: integer
          format: int32
          description: Specifies the maximum number of transactions allowed within the specified frequency period.
          example: 100
        frequency:
          type: string
          description: Defines how often transactions can occur
          example: WEEK
        amount:
          type: number
          format: double
          description: Fixed amount for transactions if applicable; null if variable amounts are allowed.
          example: null
          nullable: true
        maximumAmount:
          type: number
          format: double
          description: The maximum allowable amount for a transaction under this agreement.
          example: 100.00
          nullable: true
        agreementType:
          type: string
          description: Type of agreement, such as variable (VARI) or fixed, dictating how amounts and terms are treated.
          example: VARI
      additionalProperties: false
    RecallPaymentAgreementLastActionResponse:
      type: object
      properties:
        paymentAgreementUID:
          type: string
          description: Id to uniquely Identify a payment agreement.
          maxLength: 35
          pattern: ^[A-Z0-9_-]
          example: BCORP12345678
        mmsId:
          type: string
          description: This is the unique ID that identifies this payment agreement.
          example: cf9cba5ea20c1e6b87aa04cc8955abcc
        paymentAgreementStatus:
          type: string
          description: This is the status of the payment agreement.
          example: Cancelled
        paymentAgreementStatusDescription:
          type: string
          description: This describes the status. For example, a status of ‘created’ will have a description of ‘Pending Payer Approval’
          example: Mandate action recalled
        actionIdentification:
          type: string
          description: unique Id used to identify pending(unapproved) actions. This id is assigned when a new payment agreement is created or when a bilateral amendment is made.
          example: ********************************
      additionalProperties: false
    SubscriptionDeleteResponse:
      type: object
      properties:
        traceId:
          type: string
          example: f2db7ad2-2411-408c-b17a-5a050d30741b
      additionalProperties: false
    SubscriptionDetailResponse:
      type: object
      required:
        - subscriptionName
        - eventName
        - isActive
      properties:
        subscriptionName:
          type: string
          maxLength: 36
          description: 'User-Defined name, to make the purpose or owner clear. Name provided by customers only.'
          pattern: '^[A-Za-z0-9_-]+'
          example: PaytoTest
        eventName:
          type: string
          description: 'The event we are subscribing to. Must be ‘PaymentAgreementNotification’ - forwards all payment agreement events to the Webhook URL.'
          example: PaymentAgreementNotification
        subscriptionId:
          type: string
          example: 734d8a9d-7ac6-44a7-9e88-867fb59f54e9
        notificationType:
          type: string
        webHookDetail:
          $ref: '#/components/schemas/WebHookDetailGet'
        isActive:
          type: boolean
          example: true
      additionalProperties: false
    SubscriptionDetailUpdateRequest:
      type: object
      required:
        - subscriptionName
        - eventName
        - isActive
      properties:
        subscriptionName:
          type: string
          maxLength: 36
          example: 'PayToTest'
          description: 'User-Defined name, to make the purpose or owner clear. Name provided by customers only.'
          pattern: '^[A-Za-z0-9_-]+'
        eventName:
          type: string
          description: 'The event we are subscribing to. Must be ‘PaymentAgreementNotification’ - forwards all payment agreement events to the Webhook URL.'
          example: 'PaymentAgreementNotification'
        webHookDetail:
          $ref: '#/components/schemas/WebHookDetail'
        isActive:
          type: boolean
          example: true
      additionalProperties: false
    SubscriptionListResponse:
      type: object
      properties:
        traceId:
          type: string
          example: 5b5d2d6e-4972-4be4-ab95-8a373b9d90c5
        subscriptionDetails:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionDetailResponse'
          nullable: true
      additionalProperties: false
    SubscriptionUpdateResponse:
      type: object
      properties:
        traceId:
          type: string
          example: 5b5d2d6e-1972-4be4-ac95-8a373b9d90c5
        subscriptionId:
          type: string
          example: 5d930864-76e9-4945-bc7c-7645e38d860b
      additionalProperties: false
    WebHookDetail:
      type: object
      required:
        - callBackUrl
      properties:
        callBackUrl:
          type: string
          example: 'http://demo62eacbbe3c14.mockable.io/pagnotification'
          description: URL of your server.
        securityToken:
          type: string
          example: Basic 13222276767676
          description: This token will be sent back with the callback in Authorization header. (Authorization &#58;  [type] [credentials]  (e.x Basic Rjc1234567890jdGMS67890U78...)) When creating the token, both 'type' and 'credentials' are required.

      additionalProperties: false
    WebHookDetailGet:
      type: object
      required:
        - callBackUrl
      properties:
        callBackUrl:
          type: string
          example: 'http://demo62eacbbe3c14.mockable.io/pagnotification'
          description: URL of your server.
      additionalProperties: false
    WebHookListResponse:
      type: object
      properties:
        notifications:
          type: array
          items:
            $ref: '#/components/schemas/WebHookResponse'
      additionalProperties: false
    WebHookResendResponse:
      type: object
      properties:
        traceId:
          type: string
          example: f1a7fbd7-cb39-4b98-b2a7-f4e0e004f152
      additionalProperties: false
    WebHookResponse:
      type: object
      properties:
        eventId:
          type: string
        sourceEventName:
          type: string
        url:
          type: string
        eventTimestamp:
          type: string
          format: date-time
        executionCompleteDateTimeUtc:
          type: string
          format: date-time
        payload:
          type: string
        method:
          type: string
        status:
          type: string
        statusDescription:
          type: string
        lastResponseCode:
          type: integer
          format: int32
        retryCount:
          type: integer
          format: int32
        clientRequestedResend:
          type: boolean
      additionalProperties: false
